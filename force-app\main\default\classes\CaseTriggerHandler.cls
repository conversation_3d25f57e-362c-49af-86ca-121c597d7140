/*
* @cicd_tests AccountAccounRelShareBatchTest
*/
public with sharing class CaseTriggerHandler {
    public CaseTriggerHandler() {
    }

    public void onAfterInsOrUpd(List<Case> newList, Map<Id, Case> newMap, Map<Id, Case> oldMap) {
        performShares(newList, newMap, oldMap);
    }

    public void onBeforeInsert(List<Case> newList) {
        List<Case> filteredList = filterCasesByRecordType(newList);
        if (!filteredList.isEmpty()) {
            CaseRulesBeforeInsert.assignDefaultOwner(filteredList);
            CaseRulesBeforeInsert.logicCaseActivityInit(filteredList);
            //metodo per escludere i CaseActivityInit che hanno Type != 'Contact'
            List<Case> filteredListLeo = CaseRulesBeforeInsert.filterCasesByLeoActivityCode(filteredList);
            if (!filteredListLeo.isEmpty()) {
                CaseRulesBeforeInsert.populateAgencyOnAutomaticCase(filteredListLeo);
                CaseRulesBeforeInsert.updateStatusToExpiredDueDate(filteredListLeo);
                CaseRulesAssignementBI.manageCaseGroupsOwnership(filteredListLeo);
                CaseRulesAssignementBI.checkPolicy(filteredListLeo);
            }
        }
    }

    public void onBeforeUpdate(List<Case> newList, Map<Id, Case> oldMap) {
        List<Case> filteredList = filterCasesByRecordType(newList);
        List<Case> listCorrect = new List<Case>();
        for (Case c : filteredList) {
            if (c.dueDate__c != oldMap.get(c.id).dueDate__c) {
                listCorrect.add(c);
            }
        }
        if (!listCorrect.isEmpty()) {
            CaseRulesBeforeInsert.updateStatusToNew(listCorrect);
        }
        CaseRulesBeforeInsert.updateStatusToClosed(filteredList);
    }

    public void onBeforeInsOrUpd(List<Case> newList) {
        List<Case> filteredList = filterCasesByRecordType(newList);
        if (!filteredList.isEmpty()) {
            CaseRulesBeforeInsert.setCasesPriority(filteredList);
        }
    }

    public static void performShares(List<Case> newList, Map<Id, Case> newMap, Map<Id, Case> oldMap) {
        if (Trigger.isInsert) {
            ConiAssignmentService.performShares(newList, newMap, oldMap);
            //NO IN UAT - START
            //ConiAssignmentHelper.checkOtherShare(
            //    ConiAssignmentHelper.createSetId(newList),
            //    String.valueOf(Case.getSObjectType())
            //);
            //NO IN UAT - END
        } else {
            List<Case> listCorrect = new List<Case>();
            Set<Id> setIdAssigned = new Set<Id>();
            for (Case c : newList) {
                if (
                    c.Agency__c != oldMap.get(c.Id).Agency__c ||
                    c.AccountId != oldMap.get(c.Id).AccountId ||
                    c.AssignedGroup__c != oldMap.get(c.Id).AssignedGroup__c ||
                    c.AssignedTo__c != oldMap.get(c.Id).AssignedTo__c ||
                    c.Status != oldMap.get(c.Id).Status
                ) {
                    listCorrect.add(c);
                    setIdAssigned.add(String.isNotEmpty(c.AssignedGroup__c) ? c.AssignedGroup__c : c.AssignedTo__c);
                }
            }
            if (!listCorrect.isEmpty()) {
                ConiAssignmentService.performShares(listCorrect, new Map<Id, Case>(listCorrect), oldMap);
                //NO IN UAT - START
                //ConiAssignmentHelper.checkOtherShare(setIdAssigned, String.valueOf(Case.getSObjectType()));
                //NO IN UAT - END
            }
        }
    }

    public void onAfterInsert(List<Case> newList){
        List<Case> filteredList = filterCasesByRecordType(newList);
        if (!filteredList.isEmpty()) {
            List<Case> filteredListLeo = CaseRulesBeforeInsert.filterCasesByLeoActivityCode(filteredList);
            if (!filteredListLeo.isEmpty()) {
                CaseRulesBeforeInsert.checkLookupAccountField(filteredListLeo);
            }
        }
    }

    public List<Case> filterCasesByRecordType(List<Case> cases) {
        Map<String, Schema.RecordTypeInfo> rtMap = Schema.SObjectType.Case.getRecordTypeInfosByName();
        Set<Id> allowedIds = new Set<Id>();
        if (rtMap.containsKey('CallMeBackUnica')) {
            allowedIds.add(rtMap.get('CallMeBackUnica').getRecordTypeId());
        }
        if (rtMap.containsKey('AttivitaContatto')) {
            allowedIds.add(rtMap.get('AttivitaContatto').getRecordTypeId());
        }
        List<Case> filtered = new List<Case>();
        for (Case c : cases) {
            if (c.RecordTypeId == null || allowedIds.contains(c.RecordTypeId)) {
                filtered.add(c);
            }
        }
        return filtered;
    }
}