<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Name</fieldItem>
                <identifier>RecordNameField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-6c8ed7af-ac46-4e13-b1a4-e36f3230de55</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Asset__c</fieldItem>
                <identifier>RecordAsset_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ServiceContract__c</fieldItem>
                <identifier>RecordContract_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-800daf7d-2fa9-40a6-9469-72f18617c9c4</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>numVisibleActions</name>
                    <value>3</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>primaryField</name>
                    <value>Facet-6c8ed7af-ac46-4e13-b1a4-e36f3230de55</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>secondaryFields</name>
                    <value>Facet-800daf7d-2fa9-40a6-9469-72f18617c9c4</value>
                </componentInstanceProperties>
                <componentName>record_flexipage:dynamicHighlights</componentName>
                <identifier>record_flexipage_dynamicHighlights</identifier>
            </componentInstance>
        </itemInstances>
        <name>header</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>lookupFieldName</name>
                    <value>ContractAsset__c.ServiceContract__c.ParentServiceContractId</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>titleFieldName</name>
                    <value>Contratto Quadro</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>updateQuickActionName</name>
                    <value>ServiceContract.urcs_updateSCQuadro</value>
                </componentInstanceProperties>
                <componentName>console:relatedRecord</componentName>
                <identifier>console_relatedRecord2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>lookupFieldName</name>
                    <value>ContractAsset__c.ServiceContract__c</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>titleFieldName</name>
                    <value>Contratto Singolo</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>updateQuickActionName</name>
                    <value>ServiceContract.urcs_updateSCSingolo</value>
                </componentInstanceProperties>
                <componentName>console:relatedRecord</componentName>
                <identifier>console_relatedRecord</identifier>
            </componentInstance>
        </itemInstances>
        <name>sidebar</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.dtRichiesta__c</fieldItem>
                <identifier>RecorddtRichiesta_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TipoRich__c</fieldItem>
                <identifier>RecordTipoRich_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TipoLuogoCons__c</fieldItem>
                <identifier>RecordTipoLuogoCons_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-dettagli-consegna-ordine-col1</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.SottoFase__c</fieldItem>
                <identifier>RecordSottoFase_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tsArrivoDestFin__c</fieldItem>
                <identifier>RecordtsArrivoDestFin_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-dettagli-consegna-ordine-col2</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-dettagli-consegna-ordine-col1</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column_dettagli_consegna_1</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-dettagli-consegna-ordine-col2</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column_dettagli_consegna_2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-sezione-dettagli-consegna-ordine</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RagSoc__c</fieldItem>
                <identifier>RecordRagSoc_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Indirizzo__c</fieldItem>
                <identifier>RecordIndirizzo_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Regione__c</fieldItem>
                <identifier>RecordRegione_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Email__c</fieldItem>
                <identifier>RecordEmail_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Tel__c</fieldItem>
                <identifier>RecordTel_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-dettagli-luogo-consegna-col1</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RagSocCentroResp__c</fieldItem>
                <identifier>RecordRagSocCentroResp_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.IndirCentroResp__c</fieldItem>
                <identifier>RecordIndirCentroResp_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RegioneCentroResp__c</fieldItem>
                <identifier>RecordRegioneCentroResp_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.EmailCentroResp__c</fieldItem>
                <identifier>RecordEmailCentroResp_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TelCentroResp__c</fieldItem>
                <identifier>RecordTelCentroResp_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-dettagli-luogo-consegna-col2</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TipoNoleggio__c</fieldItem>
                <identifier>RecordTipoNoleggio_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.IdVerbale__c</fieldItem>
                <identifier>RecordIdVerbale_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CausVerb__c</fieldItem>
                <identifier>RecordCausVerb_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tsDa__c</fieldItem>
                <identifier>RecordtsDa_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-dettagli-verbale-col1</name>
        <type>Facet</type>
    </flexiPageRegions>
    <!-- Sezione 3: Dettagli Verbale - Colonna 2 -->
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tsA__c</fieldItem>
                <identifier>RecordtsA_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tsUscita__c</fieldItem>
                <identifier>RecordtsUscita_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.KmUscita__c</fieldItem>
                <identifier>RecordKmUscita_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.KmRientro__c</fieldItem>
                <identifier>RecordKmRientro_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-dettagli-verbale-col2</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-dettagli-luogo-consegna-col1</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column_luogo_consegna_1</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-dettagli-luogo-consegna-col2</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column_luogo_consegna_2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-sezione-dettagli-luogo-consegna</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-dettagli-verbale-col1</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column_verbale_1</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-dettagli-verbale-col2</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column_verbale_2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-sezione-dettagli-verbale</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-sezione-dettagli-consegna-ordine</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Dettagli Consegna Ordine</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection_dettagli_consegna_ordine</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-sezione-dettagli-luogo-consegna</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Dettagli riferimento luogo di consegna</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection_dettagli_luogo_consegna</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-sezione-dettagli-verbale</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Dettagli Verbale</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection_dettagli_verbale</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-823000d6-4ba6-4070-8f12-b9afdf2abe1f</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>NewCase</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>adminFilters</name>
                    <valueList>
                        <valueListItems>
                            <value>CASES.STATUS|NOT_EQUAL|[&quot;Chiuso - Risolto&quot;,&quot;Chiuso - Annullato&quot;]</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>30</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>ContractAsset__c.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Cases__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>CASES.CASE_NUMBER</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CASES.CREATED_DATE</value>
                        </valueListItems>
                        <valueListItems>
                            <value>EntChiamante__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Categoria__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>SottoCategoria__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CASES.STATUS</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CASES.ORIGIN</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Case Aperti</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>CASES.CREATED_DATE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Descending</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>NewCase</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>adminFilters</name>
                    <valueList>
                        <valueListItems>
                            <value>CASES.STATUS|EQUALS|[&quot;Chiuso - Risolto&quot;,&quot;Chiuso - Annullato&quot;]</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>30</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>ContractAsset__c.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Cases__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>CASES.CASE_NUMBER</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CASES.CLOSED_DATE</value>
                        </valueListItems>
                        <valueListItems>
                            <value>EntChiamante__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Categoria__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>SottoCategoria__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CASES.ORIGIN</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Case Chiusi</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>CASES.CLOSED_DATE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Descending</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList3</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-y2g6h4q1vsk</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>New</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>adminFilters</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>30</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>ContractAsset__c.ServiceContract__c</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>ContractDriver__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Account__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>TipoDelega__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Util_Dal__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Util_Al__c</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Utilizzatori del contratto</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>Util_Dal__c</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Descending</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-0be099c8-5536-43d9-8f77-def952ee3395</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-823000d6-4ba6-4070-8f12-b9afdf2abe1f</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.detail</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-y2g6h4q1vsk</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Case</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-0be099c8-5536-43d9-8f77-def952ee3395</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Utilizzatori</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-d4e12610-459a-4de0-b13e-ac2cf09d6b53</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Tabs</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>Facet-d4e12610-459a-4de0-b13e-ac2cf09d6b53</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset</identifier>
            </componentInstance>
        </itemInstances>
        <name>main</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>urcs_ContractAsset</masterLabel>
    <sobjectType>ContractAsset__c</sobjectType>
    <template>
        <name>flexipage:recordHomeLeftSidebarTemplateDesktop</name>
        <properties>
            <name>enablePageActionConfig</name>
            <value>false</value>
        </properties>
    </template>
    <type>RecordPage</type>
</FlexiPage>
