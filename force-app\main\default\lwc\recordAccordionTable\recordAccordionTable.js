import { LightningElement, api, track, wire } from "lwc";
import { ShowToastEvent } from "lightning/platformShowToastEvent";
import { NavigationMixin } from "lightning/navigation";
import { getData<PERSON>and<PERSON> } from "omnistudio/utility";
import hasCustomPermission from "@salesforce/customPermission/X169_506000000";
import hasVariazionePermission1 from "@salesforce/customPermission/X172_201030200";
import hasVariazionePermission2 from "@salesforce/customPermission/X172_201030300";
import hasVariazionePermission3 from "@salesforce/customPermission/X172_201030400";
import hasVariazionePermission4 from "@salesforce/customPermission/X172_201030500";
import hasVariazionePermission5 from "@salesforce/customPermission/X172_201030600";
import hasVariazionePermission6 from "@salesforce/customPermission/X172_201030700";
import hasVariazionePermission7 from "@salesforce/customPermission/X172_201030800";
import hasVariazionePermission8 from "@salesforce/customPermission/X172_201030900";
import hasVariazionePermission9 from "@salesforce/customPermission/X172_201031000";
import hasVariazionePermission10 from "@salesforce/customPermission/X172_202020000";
import hasVariazionePermission11 from "@salesforce/customPermission/X172_202020100";
import hasVariazionePermission12 from "@salesforce/customPermission/X172_202020101";
import hasVariazionePermission13 from "@salesforce/customPermission/X172_202020102";
import hasVariazionePermission14 from "@salesforce/customPermission/X172_202020103";
import hasVariazionePermission15 from "@salesforce/customPermission/X172_202020104";
import hasVariazionePermission16 from "@salesforce/customPermission/X172_202020105";
import hasVariazionePermission17 from "@salesforce/customPermission/X172_202020500";
import hasVariazionePermission18 from "@salesforce/customPermission/X172_202020501";
import hasVariazionePermission19 from "@salesforce/customPermission/X172_202020502";
import hasVariazionePermission20 from "@salesforce/customPermission/X172_202020503";
import hasVariazionePermission21 from "@salesforce/customPermission/X172_202020504";
import hasVariazionePermission22 from "@salesforce/customPermission/X172_202150504";
import hasVariazionePermission23 from "@salesforce/customPermission/X172_202150600";
import hasVariazionePermission24 from "@salesforce/customPermission/X172_202020600";
import hasVariazionePermission25 from "@salesforce/customPermission/X172_202150000";
import hasVariazionePermission26 from "@salesforce/customPermission/X172_202150101";
import hasVariazionePermission27 from "@salesforce/customPermission/X172_202150102";
import hasVariazionePermission28 from "@salesforce/customPermission/X172_202150100";
import hasVariazionePermission29 from "@salesforce/customPermission/X172_202150103";
import hasVariazionePermission30 from "@salesforce/customPermission/X172_202150104";
import hasVariazionePermission31 from "@salesforce/customPermission/X172_202150105";
import hasVariazionePermission32 from "@salesforce/customPermission/X172_202150106";
import hasVariazionePermission33 from "@salesforce/customPermission/X172_202150107";
import hasVariazionePermission34 from "@salesforce/customPermission/X172_202150200";
import hasVariazionePermission35 from "@salesforce/customPermission/X172_202150300";
import hasVariazionePermission36 from "@salesforce/customPermission/X172_202150301";
import hasVariazionePermission37 from "@salesforce/customPermission/X172_202150302";
import hasVariazionePermission38 from "@salesforce/customPermission/X172_202150303";
import hasVariazionePermission39 from "@salesforce/customPermission/X172_202150304";
import hasVariazionePermission40 from "@salesforce/customPermission/X172_202150305";
import hasVariazionePermission41 from "@salesforce/customPermission/X172_202150400";
import hasVariazionePermission42 from "@salesforce/customPermission/X172_202150500";
import hasVariazionePermission43 from "@salesforce/customPermission/X172_202150501";
import hasVariazionePermission44 from "@salesforce/customPermission/X172_202150502";
import hasVariazionePermission45 from "@salesforce/customPermission/X172_202150503";
import hasVisualizzazionePermission1 from "@salesforce/customPermission/X174_201000000";
import hasVisualizzazionePermission2 from "@salesforce/customPermission/X174_202000000";
import hasSostituzionePermission1 from "@salesforce/customPermission/X182_201010000";
import hasSostituzionePermission2 from "@salesforce/customPermission/X182_201020000";
import hasSostituzionePermission3 from "@salesforce/customPermission/X182_202030000";
import hasSostituzionePermission4 from "@salesforce/customPermission/X182_202120000";
import hasSostituzionePermission5 from "@salesforce/customPermission/X182_202010000";
import hasSostituzionePermission6 from "@salesforce/customPermission/X182_202140000";
import hasSostituzionePermission7 from "@salesforce/customPermission/X182_202200100";
import hasSostituzionePermission8 from "@salesforce/customPermission/X182_202200200";
import hasSostituzionePermission9 from "@salesforce/customPermission/X182_202210100";
import hasSostituzionePermission10 from "@salesforce/customPermission/X182_202210200";
import hasSostituzionePermission11 from "@salesforce/customPermission/X182_206110100";
import hasSostituzionePermission12 from "@salesforce/customPermission/X182_206110200";
import { CurrentPageReference } from 'lightning/navigation';
import uniRuota from '@salesforce/resourceUrl/uniRuota';
import uniCasa from '@salesforce/resourceUrl/uniCasa';

import checkAbbinato from '@salesforce/apex/InsurancePolicyController.checkAbbinato';

export default class RecordAccordionTable extends NavigationMixin(LightningElement) {
  @api recordId;

  @track rows = [];
  @track otherPolicies = [];
  @track isLoading = false;
  @track expandedRows = {};
  @track rowIdSelected = false;
  @track agencyType = 'same';
  @track isAbbinato = false;
  hasAperturaSinistroPermission = hasCustomPermission;
  isFlowModalOpened = false;
  flowInputs = [];

  _selectedTab;
  _policies;

  @api
  set selectedTab(value) {
    this._selectedTab = value;
    this.tryLoadingData();
  }

  get selectedTab() {
    return this._selectedTab;
  }

  @api
  set policies(value) {
    this._policies = value;
    this.tryLoadingData();
  }

  get policies() {
    return this._policies;
  }

  get motorIcon() {
    return uniRuota;
  }

  get casaIcon() {
    return uniCasa;
  }

  tryLoadingData() {
    if (this._selectedTab != null) {
      checkAbbinato({
            accountId: this.recordId
          }
        ).then( (data) => {
          this.isAbbinato = data.isAbbinato;
          this.loadDataByTab();
        })
    }
  }

  loadDataByTab() {
    this.isLoading = true;
    this.expandedRows = {};

    //const isReadOnly = this._selectedTab === "tutti";
    const isReadOnly = !this.isAbbinato;

    setTimeout(() => {
      const normalizedPolicies = Array.isArray(this._policies)
        ? this._policies
        : this._policies
        ? [this._policies]
        : [];

      this.rows = normalizedPolicies.map((p, index) => {
        let premioFormatted = "-";
        if (p.Role !== "Beneficiario" && p.Role !== "Assicurato") {
          if (
            !isNaN(p["InsurancePolicy.NPI__r.GrossWrittenPremium__c"]) &&
            typeof p["InsurancePolicy.NPI__r.GrossWrittenPremium__c"] === "number"
          ) {
            premioFormatted = `${p["InsurancePolicy.NPI__r.GrossWrittenPremium__c"].toFixed(
              2
            )} €`;
          }
        }

        let scadenza = "-";
        if (
          p["InsurancePolicy.ExpirationDate"] &&
          typeof p["InsurancePolicy.ExpirationDate"] === "string"
        ) {
          const expirationDate = new Date(p["InsurancePolicy.ExpirationDate"]);
          const today = new Date();
          const maxDate = new Date();
          maxDate.setFullYear(today.getFullYear() + 200);

          if (expirationDate <= maxDate) {
            const day = String(expirationDate.getDate()).padStart(2, '0');
            const month = String(expirationDate.getMonth() + 1).padStart(2, '0');
            const year = expirationDate.getFullYear();
            scadenza = `${day}/${month}/${year}`;
          } else {
            scadenza = "-";
          }
        }

        return {
          id: p.Id || `row-${index}`,
          nomeProdotto: p["InsurancePolicy.AreasOfNeed__c"] || "-",
          idPolizza: (p["InsurancePolicy.PolicyBranchCode__c"] && p["InsurancePolicy.ReferencePolicyNumber"]) ? (p["InsurancePolicy.PolicyBranchCode__c"] + p["InsurancePolicy.ReferencePolicyNumber"]) : "-",
          idPolizzaRecordId: p["InsurancePolicy.Id"], //
          insuranceProduct: p["InsurancePolicy.Product__c"], //
          insuranceNamedInsuranceCF: p["InsurancePolicy.NameInsured.CF__c"], //
          ruoloCliente: p.Role || "-",
          premio: premioFormatted,
          scadenza: scadenza,
          iconName: isReadOnly ? "" : "utility:chevronright",
          isExpanded: false,
          showDropdown: false,
          isLoadingDetails: false,
          hasDetailsLoaded: false,
          rowClass: "accordion-row",
          isReadOnly: isReadOnly,
          ServiceProviderIdentifier: p["InsurancePolicy.ParentPolicy.ServiceProviderIdentifier__c"], //mark
          idTecPosition: p["InsurancePolicy.ServiceProviderIdentifier__c"], //mark]
          hasMotor: false,
          hasCasaEFamiglia: false,
          hasPersona: false,
          isUnica: p["InsurancePolicy.RecordType.DeveloperName"] === "PU_FOLDER" || p["InsurancePolicy.RecordType.DeveloperName"] === "PU_POSITION",
          isEssig: p["InsurancePolicy.RecordType.DeveloperName"] !== "PU_FOLDER" && p["InsurancePolicy.RecordType.DeveloperName"] !== "PU_POSITION",
          fractionation: this.getFractionationFromCode(p["InsurancePolicy.PremiumFrequency"]) ?? '-',
          society: p["InsurancePolicy.Society__r.ExternalId__c"],
          policyType: p["InsurancePolicy.RecordType.DeveloperName"]
        };
      });
      this.isLoading = false;
    }, 300);
  }
  /* handlePremio(ruoloCliente, premioForomatted) {
  if(ruoloCliente === 'Beneficiario') {
    return '-';
  } else {
    return premioForomatted;
  }
} */
  navigateToRecord(event) {
    const rowId = event.currentTarget.dataset.id;
    console.log("row id --> " + rowId);
    const row = this.rows.find((r) => r.id === rowId);
    if (!row || !row.idPolizzaRecordId) return;
    
    if (row.isUnica) {
      this.flowInputs = [
          { name: 'FEIID', type: 'String', value: 'CONSULTAZIONE_CONTRATTO'},
          { name: 'recordId', type: 'String', value: row.idPolizzaRecordId },
          { name: 'society', type: 'String', value: row.society }                   
      ];
      this.toggleFlowModal();
    } else {
      this[NavigationMixin.Navigate]({
        type: "standard__recordPage",
        attributes: {
          recordId: row.idPolizzaRecordId,
          actionName: "view",
        },
      });
    }
    
  }
  @track tempVar;
  toggleDetails(event) {
    const rowId = event.currentTarget.dataset.id;
    console.log("row id --> " + rowId);
    const row = this.rows.find((r) => r.id === rowId);
    console.log("row  --> " + JSON.stringify(row.ServiceProviderIdentifier));
    this.tempVar = row.ServiceProviderIdentifier;
    console.log("TEMP  --> " + JSON.stringify(this.tempVar));
    if (row?.isReadOnly) return;

    const alreadyExpanded = this.expandedRows[rowId];

    // Espandere subito l'accordion (senza aspettare il caricamento dei dati)
    this.expandedRows = {
      ...this.expandedRows,
      [rowId]: !alreadyExpanded,
    };

    this.updateIcons();

    // Eseguiamo l'auto-scroll se necessario prima che i dati vengano caricati
    const btn = event.currentTarget; // Otteniamo il bottone direttamente dall'evento
    if (btn) {
      const rect = btn.getBoundingClientRect();
      const accordionHeight = 400; // altezza stimata dell'accordion aperto
      const spaceBelow = window.innerHeight - rect.bottom;

      // Se lo spazio sotto è insufficiente, eseguiamo lo scroll
      if (spaceBelow < accordionHeight) {
        setTimeout(() => {
          btn.scrollIntoView({ behavior: "smooth", block: "center" });
        }, 50);
      }
    }

    // Se non ha ancora i dettagli, caricali
    if (!alreadyExpanded && !row.hasDetailsLoaded) {
      this.rows = this.rows.map((r) =>
        r.id === rowId ? { ...r, isLoadingDetails: true } : r
      );

      // Caricamento dei dettagli (simulato con un delay)
      this.loadDetailsForRow(rowId).then(() => {
        this.expandedRows = {
          ...this.expandedRows,
          [rowId]: true, // Dopo aver caricato i dati, l'accordion sarà espanso
        };
        this.updateIcons();
      });
    }
  }
  toggleSection(event) {
    const sectionId = event.currentTarget.dataset.id;
    const rowId = event.currentTarget.dataset.rowid;

    // Trova la riga e aggiorna la sezione
    this.rows = this.rows.map((row) => {
      if (row.id !== rowId) return row;
      return {
        ...row,
        sections: row.sections.map((section) => {
          if (section.id !== sectionId) return section;
          return {
            ...section,
            isOpen: !section.isOpen,
            iconName: section.isOpen
              ? "utility:chevronright"
              : "utility:chevrondown",
          };
        }),
      };
    });
  }

  updateIcons() {
    this.rows = this.rows.map((row) => ({
      ...row,
      iconName: row.isReadOnly
        ? ""
        : this.expandedRows[row.id]
        ? "utility:chevrondown"
        : "utility:chevronright",
      isExpanded: !!this.expandedRows[row.id],
    }));
  }

  loadDetailsForRow(rowId) {
    return new Promise((resolve) => {
      // Spinner attivo subito
      this.rows = this.rows.map((row) =>
        row.id === rowId ? { ...row, isLoadingDetails: true } : row
      );

      const simulateError = false;

      if (simulateError) {
        this.showErrorToast("Errore nel recupero dei dettagli.");
        this.rows = this.rows.map((row) =>
          row.id === rowId ? { ...row, isLoadingDetails: false } : row
        );
        resolve(false); // False = errore
        return;
      }
      console.log("quaaaaa" + this.tempVar);
      const row = this.rows.find((r) => r.id === rowId);

      //const values = this.getMockData(rowId);
      this.callIntegrationProcedure(row.idPolizzaRecordId, rowId);
      /**
  const sections = this.getSectionsByTab(this._selectedTab, values);

  this.rows = this.rows.map(row => row.id === rowId ? {
      ...row,
      sections: sections,
      isLoadingDetails: false,
      hasDetailsLoaded: true
  } : row);
   
   */
    });
  }

  getSectionsByTab(tab, values) {
    const tabsConfig = {
      motor: [
        {
          id: "dates",
          title: "FRAZIONAMENTO E DATE",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "Data effetto", value: values.dataEffetto },
            { label: "Addebito ricorrente", value: values.addebitoRicorrente }, // si/no
            {
              label: "Metodo di pagamento (associato)",
              value: values.metodoPagamento,
            },
            { label: "Data incasso", value: values.dataIncasso },
            { label: "Ultimo titolo incassato", value: values.ultimoIncasso },
          ],
        },
        {
          id: "prod",
          title: "CARATTERISTICHE PRODOTTO",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "ID Folder", value: values.policyNumber }, // solo Unica
            { label: "CIP di Polizza", value: values.cip },
            { label: "Targa", value: values.targa },
            { label: "Modello Auto", value: values.modello },
            { label: "Bene assicurato", value: values.beneAssicurato }, // solo Unica
            { label: "PROD (Codice + Descrizione)", value: values.prod },
            { label: "Descrizione CIP Polizza", value: values.descrizioneCip },
            { label: "Stato polizza/posizione", value: values.statoPolizza },
            { label: "Agenzia di riferimento", value: values.agenzia },
          ],
        },
        {
          id: "info",
          title: "INFO AGGIUNTIVE",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "Codice convenzione", value: values.codiceConvenzione },
            { label: "Nome convenzione", value: values.nomeConvenzione },
            { label: "Presenza box", value: values.presenzaBox },
            { label: "Valore canone box", value: values.canoneBox },
            { label: "Nr. Unibox", value: values.nrUnibox },
            { label: "Esito Prevenitvass", value: values.esitoPreventivass },
            { label: "FEA", value: values.fea },
            { label: "Canale di acquisto", value: values.canaleAcquisto },
            { label: "Sospensione polizza", value: values.sospensione },
            { label: "CARE", value: values.care },
            { label: "Voucher", value: values.voucher },
            { label: "Suggerimento di up-selling", value: values.upSelling }, // solo se valorizzato
          ],
        },
      ],
      casa: [
        {
          id: "prod",
          title: "CARATTERISTICHE PRODOTTO",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "ID Folder", value: values.policyNumber }, // solo Unica
            { label: "CIP di Polizza", value: values.cip },
            { label: "Bene assicurato", value: values.beneAssicurato }, // solo Unica
            { label: "PROD (Codice + Descrizione)", value: values.prod },
            {
              label: "Descrizione CIP Polizza / Soggetto abbinato",
              value: values.descrizioneCip,
            },
            { label: "Stato polizza", value: values.statoPolizza },
            { label: "Agenzia di riferimento", value: values.agenzia },
          ],
        },
        {
          id: "dates",
          title: "FRAZIONAMENTO E DATE",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "Data effetto", value: values.dataEffetto },
            { label: "Addebito ricorrente", value: values.addebitoRicorrente },
            {
              label: "Metodo di pagamento (associato)",
              value: values.metodoPagamento,
            },
            { label: "Ultimo titolo incassato", value: values.ultimoIncasso },
            { label: "Data incasso", value: values.dataIncasso }, // Mostrare solo se valorizzato
          ],
        },
        {
          id: "info",
          title: "INFO AGGIUNTIVE",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "Codice convenzione", value: values.codiceConvenzione },
            { label: "Nome convenzione", value: values.nomeConvenzione },
            { label: "FEA", value: values.fea },
            { label: "Canale di acquisto", value: values.canaleAcquisto },
            { label: "Voucher", value: values.voucher },
            { label: "Suggerimento di up-selling", value: values.upSelling },
          ],
        },
      ],
      persona: [
        {
          id: "prod",
          title: "CARATTERISTICHE PRODOTTO",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "ID Folder", value: values.policyNumber }, // solo Unica
            { label: "CIP di Polizza", value: values.cip },
            { label: "Bene assicurato", value: values.beneAssicurato }, // solo Unica
            { label: "PROD (Codice + Descrizione)", value: values.prod },
            {
              label: "Descrizione CIP Polizza / Soggetto abbinato",
              value: values.descrizioneCip,
            },
            { label: "Stato polizza", value: values.statoPolizza },
            { label: "Agenzia di riferimento", value: values.agenzia },
          ],
        },
        {
          id: "dates",
          title: "FRAZIONAMENTO E DATE",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "Data effetto", value: values.dataEffetto },
            { label: "Addebito ricorrente", value: values.addebitoRicorrente },
            {
              label: "Metodo di pagamento (associato)",
              value: values.metodoPagamento,
            },
            { label: "Ultimo titolo incassato", value: values.ultimoIncasso },
            { label: "Data incasso", value: values.dataIncasso }, // Mostrare solo se valorizzato
          ],
        },
        {
          id: "info",
          title: "INFO AGGIUNTIVE",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "Codice convenzione", value: values.codiceConvenzione },
            { label: "Nome convenzione", value: values.nomeConvenzione },
            { label: "FEA", value: values.fea },
            { label: "Canale di acquisto", value: values.canaleAcquisto },
            { label: "Voucher", value: values.voucher },
            { label: "Suggerimento di up-selling", value: values.upSelling },
          ],
        },
      ],
      attivita: [
        {
          id: "prod",
          title: "CARATTERISTICHE PRODOTTO",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "ID Folder", value: values.policyNumber }, // solo Unica
            { label: "CIP di Polizza", value: values.cip },
            { label: "Bene assicurato", value: values.beneAssicurato }, // solo Unica
            { label: "PROD (Codice + Descrizione)", value: values.prod },
            {
              label: "Descrizione CIP Polizza / Soggetto abbinato",
              value: values.descrizioneCip,
            },
            { label: "Stato polizza", value: values.statoPolizza },
            { label: "Agenzia di riferimento", value: values.agenzia },
          ],
        },
        {
          id: "dates",
          title: "FRAZIONAMENTO E DATE",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "Data effetto", value: values.dataEffetto },
            { label: "Addebito ricorrente", value: values.addebitoRicorrente },
            {
              label: "Metodo di pagamento (associato)",
              value: values.metodoPagamento,
            },
            { label: "Ultimo titolo incassato", value: values.ultimoIncasso },
            { label: "Data incasso", value: values.dataIncasso }, // Mostrare solo se valorizzato
          ],
        },
        {
          id: "info",
          title: "INFO AGGIUNTIVE",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "Codice convenzione", value: values.codiceConvenzione },
            { label: "Nome convenzione", value: values.nomeConvenzione },
            { label: "FEA", value: values.fea },
            { label: "Canale di acquisto", value: values.canaleAcquisto },
            { label: "Voucher", value: values.voucher },
            { label: "Suggerimento di up-selling", value: values.upSelling },
          ],
        },
      ],
      vita: [
        {
          id: "prod",
          title: "CARATTERISTICHE PRODOTTO",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            {
              label: "Stato polizza e Causale",
              value: values.statoPolizzaCausale,
            },
            { label: "Codice convenzione", value: values.codiceConvenzione },
            { label: "Agenzia", value: values.agenzia },
            { label: "CIP", value: values.cip },
          ],
        },
        {
          id: "premi",
          title: "PREMI E DATE",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "Tipo Premio", value: values.tipoPremio },
            {
              label: "Data ultimo premio pagato",
              value: values.dataUltimoPremioPagato,
            },
            { label: "Ultimo premio pagato", value: values.ultimoPremioPagato },
            { label: "Data effetto", value: values.dataEffetto },
          ],
        },
        {
          id: "valori",
          title: "VALORI AD OGGI",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            {
              label: "Prestazione Assicurata (€)",
              value: values.prestazioneAssicurata,
            },
            { label: "Valore riscatto (€)", value: values.valoreRiscatto },
            {
              label: "Cumulo premi attivi (€)",
              value: values.cumuloPremiAttivi,
            },
          ],
        },
        {
          id: "info",
          title: "ULTERIORI INFORMAZIONI",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            {
              label: "Fascicolo Informativo / Set Informativo",
              value: values.linkFascicolo, // esempio: '<a href="..." target="_blank">Scarica Documento</a>'
              isRichText: true, // se gestito lato front per render HTML
            },
            {
              label: "Motori Finanziari Gestione Separata - GEST 1 UNIPOL",
              value: values.linkMotoriFinanziari, // esempio: '<a href="..." target="_blank">Consulta in UEBA</a>'
              isRichText: true,
            },
          ],
        },
      ],
      salute: [
        {
          id: "prod",
          title: "CARATTERISTICHE PRODOTTO",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "CIP di Polizza", value: values.cip },
            { label: "Bene assicurato", value: values.beneAssicurato },
            { label: "Modulo", value: values.modulo }, // esempio: essential + myfamily
            { label: "PROD (Codice + Descrizione)", value: values.prod },
            {
              label: "Descrizione CIP Polizza / Soggetto abbinato",
              value: values.descrizioneCip,
            },
            { label: "Stato polizza/posizione", value: values.statoPolizza },
            { label: "Agenzia di riferimento", value: values.agenzia },
          ],
        },
        {
          id: "dates",
          title: "FRAZIONAMENTO E DATE",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "Data effetto", value: values.dataEffetto },
            { label: "Addebito ricorrente", value: values.addebitoRicorrente },
            {
              label: "Metodo di pagamento (associato)",
              value: values.metodoPagamento,
            },
            { label: "Ultimo titolo incassato", value: values.ultimoIncasso },
            { label: "Data incasso", value: values.dataIncasso }, // solo se valorizzato
          ],
        },
        {
          id: "info",
          title: "INFO AGGIUNTIVE",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "Codice convenzione", value: values.codiceConvenzione },
            { label: "Nome convenzione", value: values.nomeConvenzione },
            { label: "FEA", value: values.fea },
            { label: "Canale di acquisto", value: values.canaleAcquisto },
            { label: "Voucher", value: values.voucher },
            { label: "Suggerimento di up-selling", value: values.upSelling },
          ],
        },
      ],
    };

    return tabsConfig[tab] || [];
  }
  // Da rimuovere
  

  handleDropdownToggle(event) {
    const rowId = event.currentTarget.dataset.id;
    console.log("row id --> " + rowId);
    const btn = event.currentTarget;
    const rect = btn.getBoundingClientRect();
    const menuHeight = 150;

    // Toggle menu aperto/chiuso
    this.rows = this.rows.map((row) => {
      const showDropdown = row.id === rowId ? !row.showDropdown : false;
      return {
        ...row,
        showDropdown,
        rowClass:
          row.id === rowId && showDropdown
            ? "accordion-row dropdown-open"
            : "accordion-row",
      };
    });

    const recordTable = this.template.querySelector(".record-table");
    const isDropdownOpening = this.rows.find(
      (r) => r.id === rowId && r.showDropdown
    );

    if (isDropdownOpening && !this.rowIdSelected) {
      recordTable.style.minHeight = `${
        recordTable.offsetHeight + menuHeight
      }px`;
      this.rowIdSelected = true;
    } else if (!isDropdownOpening && this.rowIdSelected) {
      recordTable.style.minHeight = `0px`;
      this.rowIdSelected = false;
    }

    const spaceRequired =
      rect.bottom + menuHeight > window.innerHeight
        ? rect.top - menuHeight
        : rect.bottom;

    if (isDropdownOpening) {
      // Auto-scroll solo se necessario
      const spaceBelow = window.innerHeight - rect.bottom;
      if (spaceBelow < menuHeight) {
        setTimeout(() => {
          btn.scrollIntoView({ behavior: "smooth", block: "center" });
        }, 50);
      }
    }
  }

  handleMenuClick(event) {
    const action = event.currentTarget.dataset.action;
    const policyId = event.currentTarget.dataset.id;
    const row = this.rows.find((r) => r.idPolizzaRecordId === policyId);
    const policyType = row.policyType.toLowerCase();
    const society = row.society;
    // For now, pass empty string as second param (to be extended later)
    const feiId = this.getFeiIdFromAction(action, policyType);
    this.flowInputs = [
        { name: 'FEIID', type: 'String', value: feiId},
        { name: 'recordId', type: 'String', value: policyId },
        { name: 'society', type: 'String', value: society }             
    ];
    const toggleEvent = {
      currentTarget: {
        dataset: {
          id: policyId
        }
      }
    };
    this.toggleFlowModal();
    this.handleDropdownToggle(toggleEvent);
  }

  getFeiIdFromAction(action, policyType) {
    const feiIdMap = {
      //generica
      'apertura_sinistro': 'SXCC.INSERIMENTO',
      //unica
      'storno_cessazione': 'UNICA.STORNO.CESSAZIONE',
      'associa_pagamento': 'UNICA.ASSOCIA.METODO.PAGAMENTO',
      'cessione': 'UNICA.CESSIONE',
      'variazione_temporanea': 'UNICA.VARIAZIONE.TEMPORANEA',
      'gestisci_contatti': 'UNICA.CONTATTI.TELEMATICA',
      //auto/nananti
      'visualizzazione_essig_auto': 'IP.INTERROGAZIONE.POLIZZA',
      'variazione_essig_auto': 'RA.VARIAZIONE.POLIZZA',
      'sostituzione_essig_auto': 'NPAC.SOSTITUZIONE.POLIZZA',
      //essig_re 
      'visualizzazione_essig_re': 'IP.INTERROGAZIONE.POLIZZA',
      'variazione_essig_re': 'RE.VARIAZIONE.POLIZZA',
      'sostituzione_essig_re': 'RE.SOSTITUZIONE.POLIZZA',
      //vita_individuale
      'visualizza_essig_vita_individuale': 'IP.INTERROGAZIONE.POLIZZA',
      'lavora_essig_vita_individuale': 'VITA.LAVORA'
    };
    const feiId = (policyType !== 'pu_position' && action !== 'apertura_sinistro') ? feiIdMap[`${action}_${policyType}`] : feiIdMap[action];
    return  feiId;
  }

  toggleFlowModal(){
    this.isFlowModalOpened = !this.isFlowModalOpened;
  }

  handleFlowStatusChange(event) {
    if (event.detail.status === 'FINISHED' || event.detail.status === 'ERROR')
        this.toggleFlowModal();
  }

  showErrorToast(message) {
    const toast = new ShowToastEvent({
      title: "Errore",
      message,
      variant: "error",
    });
    this.dispatchEvent(toast);
  }

  callIntegrationProcedure(idPolizzaRecordId, rowId) {
    console.log("dentro la chiamata " + idPolizzaRecordId);
    this.isLoading = true;
    const inputMap = {
      policyId: idPolizzaRecordId
    };
    const config = JSON.stringify({
      type: "integrationprocedure",
      value: {
        ipMethod: "ProdottiAssicurativiDetails_GetDataRow",
        inputMap: JSON.stringify(inputMap),
      },
    });
    getDataHandler(config)
      .then((result) => {
        // Verifica se il risultato è una stringa JSON e parsalo
        let parsedResult;
        try {
          parsedResult =
            typeof result === "string" ? JSON.parse(result) : result;
          console.log("parsedResult --> " + JSON.stringify(parsedResult));
        } catch (e) {
          this.isLoading = false;
          console.error("Errore parsing JSON:", e);
          this.showErrorToast("Errore nel parsing della risposta JSON.");
          return;
        }

        const data = parsedResult?.IPResult;
        if (data) {
          //elaboro data
          let values = {};
          let otherPolicies = [];
          let area = '';
          
          const selectedRow = this.rows.find((row) => row.id === rowId);

          if (selectedRow.isUnica){ 
            values = this.filterUnicaValues(data);
            area = this.getUnicaAreaFromCode(values.code);
          }
          if (selectedRow.isEssig) { 
            values = this.filterEssigValues(data, selectedRow.nomeProdotto);
            area = this.getEssigAreaFromAreaOfNeed(selectedRow.nomeProdotto);
          }

          //filters out current policy and populates list for checks
          if (data.response.positions) {
            otherPolicies = this.getOtherPolicies(data.response.positions, values.code); 
          }
          const sections = this.getSectionsByTab(area, values);

          this.rows = this.rows.map((row) =>
            row.id === rowId
              ? {
                  ...row,
                  sections: sections,
                  isLoadingDetails: false,
                  hasDetailsLoaded: true,
                  hasMotor: otherPolicies.some(p => p.code === "PUAUTO" || p.code === "PUMOBILITA"),
                  hasCasaEFamiglia: otherPolicies.some(
                    p => p.code === "PUPET" ||
                    p.code === "PUCASA" ||
                    p.code === "PUFAM" ||
                    p.code === "PUVIAGGIT"
                  ),
                  hasPersona: otherPolicies.some(p => p.code === "PUINF" || p.code === "PUSAL"),
                }
              : row
          );

          // Seleziona la sezione corretta: same_user_agency o other_agency
          /*     this.activeAgencyData = data.same_user_agency || data.other_agency || {};
          this.updateTabsWithCounts(this.activeAgencyData); */
        } else {
          console.warn("Struttura inattesa nella risposta:", parsedResult);
          this.isLoading = false;
          this.showErrorToast(
            "Struttura inattesa nella risposta della Integration Procedure."
          );
        }
      })
      .catch((error) => {
        console.error("Errore nella chiamata alla IP:", error);
        this.showErrorToast(
          "Errore durante la chiamata alla Integration Procedure."
        );
      }).finally(() => {
        this.isLoading = false;
      });
  }

  filterUnicaValues(IPResult) {
    const data = IPResult.response;
    const agencyCode = IPResult.agencyCode;
    const filteredPosition = data.positions.find(item => item.idTecPosition === IPResult.idTecPosition);
      
    if (!filteredPosition) {
      console.warn(`No position found with idTecPosition: ${data.idTecPosition}`);
      return null;
    }

    // Create base formatted object
    const formattedResult = {
      code: filteredPosition.code,
      policyNumber: data.policyNumber ? data.policyNumber : '',
      cip: data.cip, //da vedere
      prod: filteredPosition.productCode + ' - ' + this.getProductFromCode(filteredPosition.productCode),
      descrizioneCip: data.subAgencyDescription,
      statoPolizza: data.policyStatus,
      agenzia: agencyCode ? agencyCode : '',
      dataEffetto: this.formatDate(filteredPosition.effectiveStartDate),
      dataIncasso: this.formatDate(filteredPosition.titleEmissionDate),
      addebitoRicorrente: filteredPosition.fractionation == "1" ? "SI" : "NO", //fractionation a livello di posizione
      metodoPagamento: this.getPaymentMethodFromCode(filteredPosition.paymentMethod),
      ultimoIncasso: filteredPosition.titleAmount ? filteredPosition.titleAmount + ' €' : '',
      codiceConvenzione: filteredPosition.conventionCode || "",
      nomeConvenzione: filteredPosition.conventionDescription || "", //da mappare dal codice
      fea: data.fea == 'FALSE' ? "NO" : "SI", //booleano facciamolo diventare sì no
      canaleAcquisto: data.channelCode,
      voucher: filteredPosition.voucherNumber || "",
      //upSelling: data.upselling,
      upSelling: ''
    };

    // Handle beneAssicurato based on tab type
    switch(filteredPosition.code) {
      case 'PUAUTO':
          const brand = this.getBrandFromCode(filteredPosition.vehicle?.brand);
          const model = this.getModelFromCode(filteredPosition.vehicle?.model);
          formattedResult.targa = filteredPosition.vehicle?.licensePlate || '';
          formattedResult.modello = model;
          formattedResult.beneAssicurato = `${filteredPosition.vehicle?.licensePlate || ''} ${brand || ''} ${model || ''}`;
          break;
      case 'PUCASA':
          const homeType = this.getHomeTypeFromCode(filteredPosition.home?.useType);
          formattedResult.beneAssicurato = `${filteredPosition.home?.address || ''} ${filteredPosition.home?.city || ''} ${homeType || ''}`;
          break;
      case 'PUFAM':
          formattedResult.beneAssicurato = `${filteredPosition.family?.firstname || ''} ${filteredPosition.family?.lastname || ''}`;
          break;
      case 'PUPET':
          const breed = this.getBreedFromCode(filteredPosition.pet?.breed);
          const petType = filteredPosition.pet?.type === '1' ? 'Cane' : 
                        filteredPosition.pet?.type === '2' ? 'Gatto' : '';
          formattedResult.beneAssicurato = `${petType} ${breed || ''}`;
          break;
      case 'PUMOBILITA':
          formattedResult.beneAssicurato = `${filteredPosition.person?.birthDate || ''}`;
          break;
      case 'PUINF':
          formattedResult.beneAssicurato = `${filteredPosition.person?.birthDate || ''}`;
          break;
      case 'PUSAL':
          formattedResult.beneAssicurato = `${filteredPosition.person?.birthDate || ''}`;
          break;
      case 'PUVIAGGIT':
          const destination = this.getDestinationFromCode(filteredPosition.travel?.destination);
          formattedResult.beneAssicurato = `${destination || ''} ${filteredPosition.travel?.startDate || ''} ${filteredPosition.travel?.endDate || ''}`;
          break;
      default:
          formattedResult.beneAssicurato = "-";
    }

    console.log('Filtered and formatted result:', formattedResult);
    
    return formattedResult;
    
  }

  filterEssigValues(IPResult, areaOfNeed) {
    const data = IPResult.response;
    const agencyCode = IPResult.agencyCode;

    const formattedResult = {
      code: '',
      policyNumber: data.policyNumber ? data.policyNumber : '',
      cip: data.cip, //da vedere
      prod: data.productName ? data.productName : '',
      descrizioneCip: data.subAgency,
      statoPolizza: data.policyStatus,
      agenzia: agencyCode ? agencyCode : '',
      dataEffetto: this.formatDate(data.effectiveStartDate),
      dataIncasso: this.formatDate(data.titleEmissionDate),
      addebitoRicorrente: data.fractionation == "1" ? "NO" : "SI", //1 sta per annuale
      metodoPagamento: this.getPaymentMethodFromCode(data.paymentMethod),
      ultimoIncasso: data.titleAmount ? data.titleAmount + ' €' : '',
      codiceConvenzione: data.conventionCode || "",
      nomeConvenzione: data.conventionDescription || "", //da mappare dal codice
      fea: data.fea == 'FALSE' ? "NO" : "SI", //booleano facciamolo diventare sì no
      canaleAcquisto: data.channelCode,
      voucher: data.voucherNumber || "",
      //upSelling: data.upselling,
      upSelling: ''
    };

    switch(areaOfNeed) {
      case 'Veicoli': //ESSIG_AUTO
        const model = data.asset[0]?.model || '';
        formattedResult.targa = data.asset[0]?.licensePlate || '';
        formattedResult.modello = model;
        formattedResult.beneAssicurato = model;
        break;
      case 'Famiglia', 'Persona': //ESSIG_RE
        formattedResult.beneAssicurato = `${data.firstname || ''} ${data.lastname || ''}`;
        break;
      case 'Vita': //ESSIG_VITA_INDIVIDUALE
        formattedResult.beneAssicurato = `${data.firstname || ''} ${data.lastname || ''}`;
        break;
      default:
        formattedResult.beneAssicurato = "-";
    }

    return formattedResult;
  }

  getOtherPolicies(policies, currentCode) {
    const otherPolicies = policies.filter(p => p.code !== currentCode);
    if (otherPolicies.length === 0) return [];
    return otherPolicies;
  }

  getFractionationFromCode(code){
    const FRACTIONATION_CODES = {
      '0': 'TEMPORANEO',
      '1': 'ANNUALE',
      '2': 'SEMESTRALE',
      '3': 'QUADRIMESTRALE',
      '4': 'TRIMESTRALE',
      '6': 'BIMESTRALE',
      '8': 'PREMIO UNICO ANTICIPATO',
      '9': 'PREMIO UNICO ANTICIPATO'
    }
    return FRACTIONATION_CODES[code];
  }

  getUnicaAreaFromCode(code) {
    const AREA_CODES = {
      'PUAUTO': 'motor',
      'PUMOBILITA': 'motor',
      'PUCASA': 'casa',
      'PUFAM': 'casa',
      'PUPET': 'casa',
      'PUVIAGGIT': 'casa',
      'PUINF': 'persona',
      'PUSAL': 'persona'
    }
    return AREA_CODES[code];
  }

  getEssigAreaFromAreaOfNeed(areaOfNeed) {
    const AREA_CODES = {
      'Veicoli': 'motor',
      'Famiglia': 'casa',
      'Vita': 'salute',
      'Persona': 'persona'
    }
    return AREA_CODES[areaOfNeed];
  }

  handleOtherPolicyClick(event) {
    const policyType = event.currentTarget.dataset.id;
    console.log("Clicked other policy ID: " + policyType);
    this.dispatchEvent(
      new CustomEvent("changetab", {
        detail: { policyType },
      })
    );

  }

  getBreedFromCode(breedCode) {
    const BREED_CODES = {
        '101': 'ALANO',
        '102': 'BARBONCINO/BARBONE',
        '103': 'BASSOTTO TEDESCO',
        '104': 'BEAGLE',
        '105': 'BORDER COLLIE',
        '106': 'BOXER',
        '107': 'BULLDOG INGLESE',
        '108': 'CANE CORSO',
        '109': 'CAVALIER KING',
        '110': 'CHIHUAHUA',
        '111': 'COCKER SPANIEL INGLESE',
        '112': 'DALMATA',
        '113': 'DEUTSCH KURZHAAR O BRACCO TEDESCO',
        '114': 'EPAGNEUL BRETON',
        '115': 'GOLDEN RETRIVER',
        '116': 'HUSKY',
        '117': 'JACK RUSSELL TERRIER',
        '118': 'LABRADOR',
        '119': 'LAGOTTO',
        '120': 'LEVRIERO',
        '121': 'MALTESE',
        '122': 'PASTORE MAREMMANO',
        '123': 'PASTORE TEDESCO',
        '124': 'POINTER',
        '125': 'SCHNAUZER',
        '126': 'SEGUGIO',
        '127': 'SETTER INGLESE',
        '128': 'SHIH TZU',
        '129': 'VOLPINO',
        '130': 'WEIMARANER',
        '131': 'METICCIO',
        '132': 'SAMOIEDO',
        '133': 'YORKSHIRE',
        '134': 'BOVARO DEL BERNESE',
        '135': 'BULLDOG FRANCESE',
        '136': 'CARLINO',
        '137': 'PINSCHER',
        '138': 'SHAR PEI',
        '139': 'TERRANOVA',
        '140': 'AKITA',
        '141': 'ALASKAN MALAMUTE',
        '142': 'AMERICAN BULLY',
        '143': 'AUSTRALIAN SHEPHERD',
        '144': 'BASSET HOUND',
        '145': 'BOLOGNESE',
        '146': 'BOSTON TERRIER',
        '147': 'BRACCO',
        '148': 'CANE DA PASTORE BELGA',
        '149': 'CANE DA PASTORE DEL CAUCASO',
        '150': 'CANE DA PASTORE SVIZZERO',
        '151': 'CANE SAN BERNARDO',
        '152': 'CHOW CHOW',
        '153': 'COLLIE',
        '154': 'FOX TERRIER',
        '155': 'LEONBERGER',
        '156': 'LUPO CECOSLOVACCO',
        '157': 'MALTIPOO',
        '158': 'PASTORE SCOZZESE',
        '159': 'PECHINESE',
        '160': 'RHODESIAN RIDGEBACK',
        '161': 'SETTER IRLANDESE',
        '162': 'SHIBA',
        '163': 'SPINONE',
        '164': 'WELSH CORGI PEMBROKE',
        '165': 'WEST HIGHLAND WHITE TERRIER',
        '300': 'AMERICAN BULLDOG',
        '301': 'AMERICAN STAFFORDSHIRE TERRIER O AMSTAFF',
        '302': 'BRIARD',
        '303': 'BULL TERRIER',
        '304': 'BULLMASTIFF',
        '305': 'CANE DA MONTAGNA DEI PIRENEI',
        '306': 'DOBERMANN',
        '307': 'DOGO ARGENTINO',
        '308': 'DOGUE DE BORDEAUX',
        '309': 'FILA BRAZILEIRO',
        '310': 'MASTINO NAPOLETANO',
        '311': 'PITT BULL',
        '312': 'ROTTWEILER',
        '313': 'STAFFORDSHIRE TERRIER',
        '314': 'TIBETAN MASTIFF BRIARD',
        '315': 'TOSA INU GIAPPONESE',
        '316': 'METICCIO (INCROCIO CON RAZZE PERICOLOSE)',
        '399': 'ALTRO'
    };

    return BREED_CODES[breedCode] || '';
  }

  getDestinationFromCode(destinationCode) {
    const DESTINATION_CODES = {
        '1101': 'ITALIA',
        '1201': 'ALBANIA',
        '1202': 'ANDORRA',
        '1203': 'AUSTRIA',
        '1204': 'BELGIO',
        '1205': 'BIELORUSSIA',
        '1206': 'BOSNIA ED ERZEGOVINA',
        '1207': 'BULGARIA',
        '1208': 'CITTA DEL VATICANO',
        '1209': 'CROAZIA',
        '1210': 'DANIMARCA',
        '1211': 'ESTONIA',
        '1212': 'FINLANDIA',
        '1213': 'FRANCIA',
        '1214': 'GERMANIA',
        '1215': 'GIBILTERRA',
        '1216': 'GRECIA',
        '1217': 'IRLANDA - EIRE',
        '1218': 'ISLANDA',
        '1219': 'ISOLA DI GUERNSEY',
        '1220': 'ISOLA DI JERSEY',
        '1221': 'ISOLA DI MAN',
        '1222': 'ISOLE FAROE',
        '1223': 'LETTONIA',
        '1224': 'LIECHTENSTEIN',
        '1225': 'LITUANIA',
        '1226': 'LUSSEMBURGO',
        '1227': 'MACEDONIA',
        '1228': 'MALTA',
        '1229': 'MONACO',
        '1230': 'MONTENEGRO',
        '1231': 'NORVEGIA',
        '1232': 'OLANDA - PAESI BASSI',
        '1233': 'POLONIA',
        '1234': 'PORTOGALLO',
        '1235': 'REGNO UNITO',
        '1236': 'REGNO UNITO - GALLESE',
        '1237': 'REGNO UNITO - INGHILTERRA',
        '1238': 'REGNO UNITO - IRLANDA DEL NORD',
        '1239': 'REGNO UNITO - SCOZIA',
        '1240': 'REPUBBLICA CECA',
        '1241': 'ROMANIA',
        '1242': 'SAN MARINO',
        '1243': 'SERBIA',
        '1244': 'SLOVACCHIA',
        '1245': 'SLOVENIA',
        '1246': 'SPAGNA',
        '1247': 'SVEZIA',
        '1248': 'SVIZZERA',
        '1249': 'UCRAINA',
        '1250': 'UNGHERIA',
        '1251': 'SVALBARD E JAN MAYEN',
        '1252': 'MOLDAVIA',
        '1301': 'ALGERIA',
        '1302': 'CIPRO',
        '1303': 'EGITTO',
        '1304': 'ISRAELE',
        '1305': 'LIBANO',
        '1306': 'LIBIA',
        '1307': 'MAROCCO',
        '1309': 'SIRIA',
        '1310': 'TUNISIA',
        '1311': 'TURCHIA',
        '1401': 'ANGOLA',
        '1402': 'BENIN',
        '1403': 'BOTSWANA',
        '1404': 'BURKINA FASO',
        '1405': 'CAMERUN',
        '1406': 'CAPO VERDE',
        '1407': 'COMORE',
        '1408': 'ERITREA',
        '1409': 'GABON',
        '1410': 'GAMBIA',
        '1411': 'GHANA',
        '1412': 'GIBUTI',
        '1413': 'GUINEA',
        '1414': 'GUINEA EQUATORIALE',
        '1415': 'GUINEA-BISSAU',
        '1416': 'KENYA',
        '1417': 'LESOTHO',
        '1418': 'MADAGASCAR',
        '1419': 'MALAWI',
        '1420': 'MALI',
        '1421': 'MAURITANIA',
        '1422': 'MAURITIUS - MAURIZIUS',
        '1423': 'MAYOTTE',
        '1424': 'MOZAMBICO',
        '1425': 'NAMIBIA',
        '1426': 'NIGER',
        '1427': 'NIGERIA',
        '1428': 'RIUNIONE - REUNION',
        '1429': 'SANT ELENA',
        '1430': 'SAO TOME E PRINCIPE',
        '1431': 'SENEGAL',
        '1432': 'SEYCHELLES',
        '1433': 'SUD AFRICA',
        '1434': 'SUDAN DEL SUD',
        '1435': 'SWAZILAND',
        '1436': 'TANZANIA',
        '1437': 'TERRITORI FRANCESI DEL SUD',
        '1438': 'TOGO',
        '1439': 'TRINIDAD E TOBAGO',
        '1440': 'ZAMBIA',
        '1441': 'ZIMBABWE',
        '1501': 'ANGUILLA',
        '1502': 'ANTIGUA E BARBUDA',
        '1503': 'ANTILLE OLANDESI',
        '1504': 'ARGENTINA',
        '1505': 'ARUBA',
        '1506': 'BAHAMAS',
        '1507': 'BARBADOS',
        '1508': 'BELIZE',
        '1509': 'BERMUDA',
        '1510': 'BOLIVIA',
        '1511': 'BRASILE',
        '1512': 'CANADA',
        '1513': 'CILE',
        '1514': 'COLOMBIA',
        '1515': 'COSTA RICA',
        '1516': 'CUBA',
        '1517': 'DOMINICA',
        '1518': 'ECUADOR',
        '1519': 'EL SALVADOR',
        '1520': 'GEORGIA DEL SUD E ISOLE SANDWICH MERIDIONALI',
        '1521': 'GIAMAICA',
        '1522': 'GRENADA',
        '1523': 'GROENLANDIA',
        '1524': 'GUADALUPA',
        '1525': 'GUATEMALA',
        '1526': 'GUYANA',
        '1527': 'GUYANA FRANCESE',
        '1528': 'HONDURAS',
        '1529': 'ISOLE BES',
        '1530': 'ISOLE CAYMAN',
        '1531': 'ISOLE COOK',
        '1532': 'ISOLE FALKLAND',
        '1533': 'ISOLE MARIANNE SETTENTRIONALI',
        '1534': 'ISOLE MINORI ESTERNE DEGLI STATI UNITI',
        '1535': 'ISOLE VERGINI AMERICANE',
        '1536': 'ISOLE VERGINI BRITANNICHE',
        '1537': 'MARTINICA',
        '1538': 'MESSICO',
        '1539': 'MONTSERRAT',
        '1540': 'NICARAGUA',
        '1541': 'PANAMA',
        '1542': 'PARAGUAY',
        '1543': 'PERU',
        '1544': 'PORTO RICO',
        '1545': 'REPUBBLICA DOMINICANA',
        '1546': 'SAINT KITTS E NEVIS',
        '1547': 'SAINT VINCENT E GRENADINE',
        '1548': 'SAINT-BARTH',
        '1549': 'SAINT-MARTIN',
        '1550': 'SAINT-PIERRE E MIQUELON',
        '1551': 'SAMOA AMERICANE',
        '1552': 'SANTA LUCIA',
        '1553': 'SINT MAARTEN',
        '1554': 'STATI UNITI D AMERICA - USA',
        '1555': 'SURINAME',
        '1557': 'TURKS E CAICOS',
        '1558': 'URUGUAY',
        '1559': 'VENEZUELA',
        '1601': 'ANTARTIDE',
        '1602': 'ISOLA BOUVET',
        '1603': 'ISOLE HEARD E MCDONALD',
        '1604': 'TERRE AUSTRALI E ANTARTICHE FRANCESI',
        '1701': 'ARABIA SAUDITA',
        '1702': 'ARMENIA',
        '1703': 'AZERBAIGIAN',
        '1704': 'BAHRAIN - BAHREIN',
        '1705': 'BANGLADESH',
        '1706': 'BHUTAN',
        '1707': 'BIRMANIA',
        '1708': 'BRUNEI',
        '1709': 'CAMBOGIA',
        '1710': 'CINA',
        '1711': 'COREA DEL SUD',
        '1712': 'EMIRATI ARABI UNITI',
        '1713': 'FEDERAZIONE RUSSA - RUSSIA',
        '1714': 'FILIPPINE',
        '1715': 'GEORGIA',
        '1716': 'GIAPPONE',
        '1717': 'GIORDANIA',
        '1718': 'HONG KONG',
        '1719': 'INDIA',
        '1720': 'INDONESIA',
        '1721': 'IRAN',
        '1722': 'KAZAKISTAN',
        '1723': 'KIRGHIZISTAN',
        '1724': 'KUWAIT',
        '1725': 'LAOS',
        '1726': 'MACAO',
        '1727': 'MALDIVE',
        '1728': 'MALESIA',
        '1730': 'MONGOLIA',
        '1731': 'OMAN',
        '1732': 'PAKISTAN',
        '1733': 'PITCAIRN',
        '1734': 'QATAR',
        '1735': 'SINGAPORE',
        '1736': 'SRI LANKA',
        '1737': 'TAGIKISTAN',
        '1738': 'TAILANDIA - THAILANDIA',
        '1739': 'TAIWAN',
        '1740': 'TERRITORI BRITANNICI DELL OCEANO INDIANO',
        '1741': 'TURKMENISTAN',
        '1742': 'UZBEKISTAN',
        '1743': 'VIETNAM',
        '1801': 'AUSTRALIA',
        '1802': 'GUAM',
        '1803': 'ISOLA DI NATALE',
        '1804': 'ISOLA NORFOLK',
        '1805': 'ISOLE COCOS (KEELING)',
        '1806': 'ISOLE FIJI - ISOLE FIGI',
        '1807': 'ISOLE MARSHALL',
        '1808': 'ISOLE MINORI DEGLI STATI UNITI D AMERICA',
        '1809': 'ISOLE SALOMONE',
        '1810': 'KIRIBATI',
        '1811': 'MICRONESIA',
        '1812': 'NAURU',
        '1813': 'NIUE',
        '1814': 'NUOVA CALEDONIA',
        '1815': 'NUOVA ZELANDA',
        '1816': 'PALAU',
        '1817': 'POLINESIA FRANCESE',
        '1818': 'SAMOA',
        '1819': 'TOKELAU',
        '1820': 'TONGA',
        '1821': 'TUVALU',
        '1822': 'VANUATU',
        '1823': 'WALLIS E FUTUNA'
    };

    return DESTINATION_CODES[destinationCode] || '';
  }

  getHomeTypeFromCode(homeTypeCode) {
    const HOME_TYPE_CODES = {
        '1': 'APPARTAMENTO',
        '2': 'VILLA SINGOLA',
        '3': 'VILLA A SCHIERA'
    };

    return HOME_TYPE_CODES[homeTypeCode] || '';
  }

  getBrandFromCode(brandCode) {
    const BRAND_CODES = {
        '8': 'FIAT',
        '13': 'FERRARI',
        '18': 'DACIA',
        '20': 'OPEL',
        '27': 'PORSCHE',
        '33': 'MERCEDES',
        '34': 'LANCIA',
        '35': 'AUTOBIANCHI',
        '39': 'FORD',
        '49': 'BERTONE',
        '53': 'SEAT',
        '62': 'RENAULT',
        '70': 'BENTLEY',
        '74': 'ASTON MARTIN',
        '76': 'CADILLAC',
        '80': 'AUSTIN ROVER',
        '81': 'BUICK',
        '83': 'ALFA ROMEO',
        '84': 'CHEVROLET',
        '85': 'BMW',
        '86': 'ROLLS-ROYCE',
        '87': 'CHRYSLER',
        '91': 'CITROEN',
        '94': 'SKODA',
        '95': 'NISSAN SPAGNA',
        '98': 'VOLVO',
        '101': 'VOLKSWAGEN',
        '103': 'TOYOTA',
        '104': 'DAIHATSU',
        '106': 'DAIMLER',
        '109': 'MORETTI',
        '110': 'JAGUAR',
        '113': 'OLTCIT',
        '114': 'DE TOMASO',
        '117': 'MASERATI',
        '120': 'INNOCENTI',
        '122': 'HYUNDAI',
        '128': 'FSO',
        '134': 'LOTUS',
        '135': 'LAMBORGHINI',
        '136': 'MORGAN',
        '137': 'PONTIAC',
        '140': 'LAND ROVER',
        '144': 'JEEP',
        '145': 'MAZDA',
        '146': 'ISUZU',
        '147': 'HONDA',
        '148': 'MITSUBISHI',
        '149': 'NISSAN',
        '151': 'PEUGEOT',
        '153': 'SAAB',
        '154': 'ROVER',
        '155': 'SUBARU',
        '156': 'SUZUKI',
        '160': 'ALPINA BMW',
        '161': 'AUDI',
        '173': 'VOLKSWAGEN MESSICO',
        '180': 'ARO',
        '193': 'DONKERVOORT',
        '196': 'GINETTA',
        '197': 'ZAZ',
        '216': 'VOLGA',
        '217': 'OTO MELARA',
        '222': 'UMM',
        '224': 'UAZ',
        '225': 'TVR',
        '240': 'PUMA ITALIA',
        '243': 'PANTHER',
        '250': 'CASALINI',
        '254': 'LIGIER',
        '255': 'SANTANA',
        '267': 'DODGE',
        '277': 'CATERHAM',
        '323': 'AC',
        '333': 'APAL',
        '397': 'TALBOT',
        '416': 'AIXAM',
        '422': 'JDM',
        '433': 'LADA',
        '436': 'RAYTON FISSORE',
        '495': 'MARCOS',
        '507': 'VENTURI',
        '515': 'AUVERLAND',
        '577': 'DAEWOO',
        '598': 'MIDDLEBRIDGE',
        '651': 'LUAZ',
        '657': 'MAHINDRA',
        '700': 'AMG',
        '717': 'OTO MELARA',
        '746': 'KIA',
        '766': 'MICROCAR',
        '772': 'P.G.O.',
        '776': 'IATO',
        '804': 'EVANTE',
        '848': 'MARUTI SUZUKI',
        '892': 'YUGO',
        '901': 'OMAI',
        '923': 'BEDFORD',
        '926': 'MICRO VET',
        '938': 'FIAT',
        '941': 'PIAGGIO',
        '945': 'ZAGATO',
        '979': 'SSANGYONG',
        '998': 'CITROEN',
        '1024': 'ACM',
        '1026': 'MOHR',
        '1036': 'TORPEDO',
        '1058': 'DE LA CHAPELLE',
        '1117': 'NOBLE',
        '1362': 'BIAGINI',
        '1421': 'ASIA MOTORS',
        '1504': 'BELLIER',
        '1522': 'BUGATTI',
        '1539': 'CHATENET',
        '1599': 'EPOCAR',
        '1852': 'KEWET',
        '1859': 'CITYCAR',
        '1938': 'MEGA',
        '1945': 'MG',
        '1960': 'MAZZIERI',
        '1962': 'LEXUS',
        '1970': 'INFINITI',
        '2092': 'MOKE',
        '2130': 'TATA',
        '2176': 'BOXEL',
        '2222': 'MINI',
        '2259': 'SMART',
        '2266': 'SAVEL-ERAD',
        '2269': 'TASSO',
        '2273': 'CARLETTI',
        '2275': 'CRECAV',
        '2277': 'SECMA',
        '2285': 'ISO',
        '2288': 'PAGANI',
        '2291': 'VALENTINI',
        '2293': 'TOWN LIFE',
        '2297': 'FEAB',
        '2300': 'QVALE',
        '2303': 'GEM',
        '2309': 'CMC',
        '2311': 'EFFEDI',
        '2313': 'BREMACH',
        '2316': 'RENAULT V.I.',
        '2318': 'IVECO',
        '2320': 'CO.VE.IN.',
        '2322': 'COMAI',
        '2324': 'MULTICAR',
        '2326': 'BONETTI',
        '2330': 'ROMANITAL',
        '2333': 'LEOMAR',
        '2336': 'O.ZETA CLES',
        '2339': 'ZK',
        '2341': 'SCAM',
        '2345': 'ARIEL',
        '2350': 'BUCHER-SCHORLING',
        '2352': 'MORONI',
        '2359': 'MAYBACH',
        '2363': 'DAF',
        '2367': 'FLEUR DE LYS',
        '2369': 'FRESIA',
        '2382': 'NYSA MOTOR',
        '2386': 'ANDORIA MOTOR',
        '2392': 'RENAULT TRUCKS',
        '2394': 'START LAB',
        '2398': 'HUMMER',
        '2401': 'MARANELLO',
        '2403': 'SECA',
        '2405': 'CORVETTE',
        '2407': 'INTRALL POLSKA',
        '2408': 'SALEEN',
        '2409': 'ABARTH',
        '2705': 'GAZ AUTOMOBILE',
        '2706': 'GREEN COMPANY',
        '2708': 'MITSUBISHI TRUCKS',
        '2709': 'ITALCAR',
        '2710': 'GIOTTILINE',
        '2712': 'MELEX',
        '2713': 'META',
        '2715': 'GREAT WALL MOTOR',
        '2716': 'KATAY',
        '2717': 'DRX',
        '2718': 'GIOTTI VICTORIA',
        '2719': 'SHUANGHUAN',
        '2736': 'I.CO.VE.CO.',
        '2737': 'CMI',
        '2738': 'BSI',
        '2740': 'GAC GONOW',
        '2743': 'MITSUBISHI FUSO',
        '2745': 'MARTIN MOTORS',
        '2749': 'TESLA',
        '2752': 'FISKER',
        '2753': 'NYSA',
        '2754': 'MC LAREN',
        '2755': 'VEM',
        '2759': 'MAN',
        '2766': 'MUSTANG',
        '2767': 'MIA ELECTRIC',
        '2768': 'HEIBAO ITALIA',
        '2769': 'I MOVING',
        '2770': 'TAZZARI EV',
        '2771': 'ALKÈ',
        '2772': 'BIRO',
        '2773': 'MINAUTO',
        '2774': 'ROMEO FERRARIS',
        '2775': 'CITROEN DS',
        '2776': 'MAZZANTI',
        '2777': 'XINDAYANG',
        '2779': 'DALLARA',
        '2780': 'ALPINE',
        '2781': 'MILITEM',
        '2782': 'CUPRA',
        '2785': 'MPM MOTORS',
        '2786': 'HAVAL',
        '2788': 'XEV',
        '2790': 'GOUPIL',
        '2791': 'EVO',
        '2792': 'DUCATI ENERGIA',
        '2793': 'MAXUS',
        '2794': 'NANOCAR',
        '2795': 'SERES',
        '2796': 'LYNK&CO',
        '2798': 'ZD',
        '2799': 'AIWAYS',
        '2800': 'INEOS',
        '2801': 'DFSK',
        '2802': 'ELI',
        '2803': 'TODAY SUNSHINE'
    };

    return BRAND_CODES[brandCode] || '';
  }

  getModelFromCode(modelCode) {
    const MODEL_CODES = {
        '4320': '500X',
        '3497': 'Multipla 2ª serie',
        '3900': 'Panamera',
        '4374': 'Tucson 2ª serie',
        '3694': 'Ypsilon 2ª serie', 
        '1029': 'LIBERTY S 50',
        '1371': 'F 800 GS',
        '387': 'VELVET 400',
        '3881': 'Insignia',
        '325': 'AREA 51',
        '2380': 'Corsa 2ª serie',
        '3641': 'IS 2ª serie',
        '3122': 'NQI',
        '923': 'R 1200 RT',
        '2826': 'SE 125',
        '3470': 'fortwo 1ª serie',
        '3072': '150 EXC',
        '3077': 'Punto 2ª serie',
        '1824': 'TRX 850',
        '3003': 'Atos',
        '985': 'AGILITY 125',
        '3788': 'Musa 2ª serie',
        '946': 'VESPA 150 LX',
        '4537': 'X2 (F39)',
        '2282': '200 DUKE',
        '480': 'Fiesta 2ª serie',
        '4221': 'S-Cross',
        '1567': 'Express',
        '352': 'Giulietta',
        '2880': 'SCRAMBLER 1100',
        '4080': 'Serie 3 (F30/F31)',
        '726': 'CALIFORNIA',
        '4570': 'Combo Life',
        '2806': 'V7 III',
        '4048': 'Picanto 3ª serie',
        '4406': 'Edge',
        '3167': 'X-Type X400/6/7/8/9',
        '2066': 'MP3 300',
        '2263': 'INTEGRA',
        '3682': 'Captiva',
        '3544': '800MT',
        '4414': 'A4 allroad 2ª serie',
        '3143': 'Corsa 3ª serie',
        '3197': 'Mini 2ª serie',
        '4125': 'Lodgy',
        '773': 'CBF 600',
        '3621': 'Grand Vitara 2ª',
        '3791': 'A4 4ª serie',
        '4358': 'Caddy 4ª serie',
        '3467': 'forfour',
        '2514': 'STREET 750',
        '4584': 'Combo 5ª serie',
        '904': 'AGILITY 50',
        '2986': 'Golf 4ª serie',
        '4157': 'Mokka',
        '4646': '208 2ª serie',
        '4667': '2008 2ª serie',
        '2950': 'TW 125 X',
        '4098': '208',
        '3244': 'TRIDENT 660',
        '3627': 'Cayman (987)',
        '3350': 'Trans/Tour Connect',
        '3133': 'RAV4 2ª serie',
        '4739': 'VESPA 125 (VNL2/VNL3)',
        '4769': 'Spring',
        '2630': 'Polo 3ª serie',
        '2268': 'XENTER 125',
        '4285': 'Mini 5 porte (F55)',
        '1377': 'GP 800',
        '356': 'SCARABEO 200',
        '2772': 'TRICITY 155',
        '4627': 'Vivaro 4ª serie',
        '323': 'YZF R6',
        '4278': 'Golf Sportsvan',
        '3538': 'Boxster (987)',
        '4713': 'VESPA 125 (VNB/VNC)',
        '2954': 'RKF 125',
        '4678': 'i10 3ª serie',
        '3121': 'MQI',
        '4803': '308 3ª serie',
        '4175': 'Kuga 2ª serie',
        '1807': 'XL 600 V TRANSALP',
        '1956': 'Clio',
        '2461': 'BURGMAN 200',
        '4019': 'A6 4ª serie',
        '5058': 'Mini (F66)',
        '3755': 'Classe C (W/S204)',
        '3901': 'Cruze',
        '4293': '695 C 1.4 TURBO T-JET 180 CV',
        '3960': 'Cayenne 3ª serie',
        '695': 'MONSTER 800',
        '6276': 'SKYTOWN 125',
        '3171': 'Classe C Sportcoupé',
        '858': 'CIAO',
        '3866': 'Q5',
        '6359': 'VESPA 310 GTS HPE',
        '4559': 'Focus 5ª serie',
        '1831': 'XJR 1200',
        '4479': 'Classe E Cpé (C238)',
        '827': 'XRV 750 AFRICA TWIN',
        '2792': 'TRK 502',
        '775': 'CBR 600',
        '3870': '',
        '3930': 'Transp. 6ª \'09->',
        '4268': 'X4 (F26)',
        '4459': 'Q5 2ª serie',
        '3709': 'Vivaro 2ª serie',
        '2807': 'X-MAX 300',
        '4916': 'VESPA 50 SUPER SPRINT',
        '498': 'TOURING ELECTRA GLIDE',
        '4834': 'dr 4.0',
        '4846': 'Mazda2 Hybrid',
        '3753': 'fortwo 2ª serie',
        '4052': 'Serie 1 (F20)',
        '692': 'MONSTER 600',
        '3911': '3008',
        '3095': 'A2',
        '2640': '146',
        '195': 'BURGMAN 400',
        '5098': 'ZT 350 D',
        '3647': 'M502',
        '4709': 'VESPA PX 150 E',
        '4044': 'Ypsilon 3ª serie',
        '1638': 'Beta Montecarlo',
        '878': 'VESPA 125 ET4',
        '4785': 'Kangoo 4ª serie',
        '281': 'T MAX',
        '4544': 'Duster 2ª serie',
        '4700': 'dr 5.0',
        '260': 'DT 125',
        '3553': 'Serie 3 (E90/E91)',
        '2450': 'NC 750',
        '4659': 'Kamiq',
        '4513': 'E-Pace (X540)',
        '366': 'SR 50',
        '253': 'AEROX 50',
        '4732': 'Mokka 2ª serie',
        '362': 'SPORTCITY 125',
        '4510': 'X3 (G01)',
        '5911': 'VALICO 900',
        '497': 'SPORTSTER 883',
        '3091': 'Premacy',
        '3759': 'Expert 3ª serie',
        '3845': 'Aveo 1ª serie',
        '4042': 'Range Rover Evoque',
        '4208': 'Ghibli',
        '4476': 'Civic 10ª serie',
        '3456': '407 1ª serie',
        '3081': 'Lybra',
        '98': 'BET & WIN 150',
        '1813': 'R 1100 GS',
        '536': 'W 650',
        '4634': 'Clio 5ª serie',
        '4913': '408',
        '2143': 'TIGER 800',
        '4281': 'e-up!',
        '5037': '3008 3ª serie',
        '1043': 'BEVERLY 400',
        '3502': 'Tucson',
        '889': 'X9 250',
        '4087': 'up!',
        '4796': 'Yaris Cross',
        '3605': '600',
        '9999999': 'modello con forzatura',
        '4259': 'C1 2ª serie',
        '3382': 'Touran',
        '3807': 'Focus 3ª serie',
        '3891': 'i20',
        '879': 'VESPA 125 L',
        '2757': 'Y 1.2 16V LS',
        '4649': 'Xceed',
        '2648': 'Galaxy 1ª serie',
        '2261': 'VISION 50',
        '3225': 'TRACER 7',
        '4443': '3008 2ª serie',
        '804': 'SILVER WING',
        '1012': 'GSR 600',
        '1127': '1098',
        '3335': 'Phedra',
        '3476': 'Altea',
        '1042': 'NEXUS 250',
        '2793': 'LEONCINO 500',
        '2780': 'Fiesta 3ª serie',
        '4869': 'Tonale',
        '4902': 'Classe GLC (X254)',
        '4924': 'e-C4 X',
        '3413': 'Vito (2ª serie)',
        '4279': 'X-Trail 3ª serie',
        '4531': 'Karoq',
        '4371': 'Astra 5ª serie',
        '3843': 'XC60 (2008--->)',
        '3879': 'Ka 2ª serie',
        '3031': 'TT',
        '4187': 'Octavia 3ª serie',
        '669': 'R 850 RT',
        '4093': 'Panda 3ª serie',
        '3851': 'Delta (2008--->)',
        '3631': 'Brera',
        '4091': 'Punto 4ª serie',
        '3018': 'SUPER CUB 125',
        '2972': 'Xsara',
        '4250': 'Classe V 2014--->',
        '2200': '19 2ª serie',
        '4639': 'Corsa 6ª serie',
        '4152': 'Golf 7ª serie',
        '4067': 'Yaris 3ª serie',
        '2788': 'X-ADV 750',
        '2543': 'SCRAMBLER',
        '5021': 'X2 (U10)',
        '3061': 'TENERE 700',
        '4804': 'Fabia 4ª serie',
        '3680': 'Ducato (4ª serie)',
        '4686': 'A3 4ª serie',
        '3321': 'DTX 360',
        '5063': 'E-Transit Custom',
        '3909': 'Scénic 3ª serie',
        '4961': 'Evo 5',
        '4653': 'Kuga 3ª serie',
        '956': 'X-MAX 250',
        '3947': 'Spark',
        '798': 'SH 125',
        '694': 'Defender',
        '538': 'Z 750',
        '4838': 'ZS (2021-->)',
        '1271': 'MULHACEN 125',
        '3261': 'V7 IV',
        '4408': 'Tiguan 2ª serie',
        '5070': 'Duster 3ª serie',
        '4381': 'F-Pace (X761)',
        '4367': 'X1 (F48)',
        '4441': 'Classe GLC Coupé',
        '779': 'DEAUVILLE',
        '4566': 'Rifter',
        '3797': 'GRANDE PUNTO PUNTO 1.4 T-JET 16V 180 CV',
        '4452': 'A3 SPORTBACK E-TRON SPB 40 E-TRON S TRO',
        '522': 'ER 5',
        '547': 'BOOSTER',
        '4917': 'HS',
        '2881': 'Passat 5ª serie',
        '2103': '106',
        '3808': 'Kangoo 3ª serie',
        '3269': 'TWEET 200',
        '2094': 'PEOPLE 300',
        '4603': 'RAV4 5ª serie',
        '4914': 'Austral',
        '3917': 'Yeti',
        '345': 'RS 125',
        '4029': 'Focus 4ª serie',
        '205': 'GSX HAYABUSA 1300',
        '272': 'MAJESTY 250',
        '4647': 'CX-30',
        '3997': 'Sportage 3ª serie',
        '1877': 'GSX 1250',
        '4363': 'Mini Clubman (F54)',
        '3236': 'H-100',
        '4300': 'Trafic 4ª serie',
        '4384': 'L200 (2015--->)',
        '3516': 'A4 3ª serie',
        '3590': 'Aygo',
        '2311': 'T MAX 530',
        '4269': '108',
        '4517': 'Polo 6ª serie',
        '358': 'SCARABEO 50',
        '4707': 'XT4',
        '1582': 'SYMPHONY 125',
        '4587': 'C5 Aircross',
        '355': 'SCARABEO 150',
        '5603': 'R 1300 GS',
        '4533': 'Kona',
        '3361': 'Daily (1996-2001)',
        '3042': 'Yaris',
        '4519': 'C3 Aircross',
        '870': 'NRG',
        '4273': 'Daily (2014--->)',
        '4511': 'Grandland X',
        '4275': 'Serie 2 Act. Tourer',
        '3958': 'ix35',
        '3999': 'V60 (2010---->)',
        '2064': 'AGILITY 200I',
        '4312': 'Twingo 3ª serie',
        '3408': 'Ypsilon',
        '3857': '500',
        '1069': 'VESPA 250 GTV',
        '3469': 'Classe SLK (R171)',
        '99': 'BET & WIN 250',
        '4133': 'B-Max',
        '2404': 'Punto',
        '409': 'RR ENDURO 50',
        '5183': 'ZS (2024-->)',
        '5611': 'V11 125',
        '343': 'PEGASO 650',
        '4751': 'Tucson 3ª serie',
        '3505': 'DESERTX',
        '4294': 'TT 3ª serie',
        '4724': 'Formentor',
        '4454': 'C-HR',
        '4063': 'Rio 3ª serie',
        '306': 'XV 535 VIRAGO',
        '3240': 'XEF 125',
        '4238': 'Classe C (W/S205)',
        '1585': 'SYMPHONY 50',
        '3024': 'Grand Vitara',
        '3954': 'CR-V 3ª serie 10-12',
        '4995': 'Z 350',
        '3689': 'Galaxy 2ª serie',
        '4799': 'T9L',
        '2599': 'FIDDLE III 125',
        '3875': 'Mégane 3ª serie',
        '3601': '159',
        '4058': 'Veloster',
        '4337': 'Serie 2 Cabrio(F23)',
        '4599': 'Corolla (2018--->)',
        '4674': 'VESPA P 200 E',
        '4232': 'Classe GLA (X156)',
        '3108': 'Voy./G.Voyager 3ª s',
        '3913': 'Polo 5ª serie',
        '227': 'V STROM DL 650',
        '3714': 'Qashqai 1ª serie',
        '3781': '500 (2007--->)',
        '3616': 'Doblò 2ª serie',
        '1827': 'K 100 RS',
        '2117': 'G 650 GS',
        '867': 'LIBERTY 125',
        '800': 'SH 50',
        '4928': 'Avenger',
        '2710': 'AGILITY 125I',
        '868': 'LIBERTY 150',
        '233': 'VZ MARAUDER 800',
        '2110': '125 DUKE',
        '1166': 'SH 300',
        '3164': 'Cherokee 2ª serie',
        '799': 'SH 150',
        '1482': 'VESPA 300 GTS',
        '192': 'AN BURGMAN 400',
        '4844': 'Sportage 5ª serie',
        '4356': 'Kadjar',
        '3007': 'Serie 3 (E46)',
        '774': 'CBR 1000',
        '2988': 'Kangoo 1ª serie',
        '308': 'XVS 1100A DRAG STAR',
        '3928': 'C3 2ª serie',
        '1393': 'PEOPLE 200I',
        '4681': 'Classe GLE Cpé C167',
        '4249': 'EcoSport',
        '5390': 'TRK 702',
        '4561': 'KUV100',
        '4496': 'Picanto 3ªs.(17-->)',
        '2991': '911 (996)',
        '3768': 'Wrangler 2ª serie',
        '4657': 'Juke 2ª serie',
        '3015': '650MT',
        '2496': 'CBR 650',
        '266': 'FZS 600 FAZER',
        '256': 'BW\'S',
        '2662': 'barchetta',
        '1280': 'CABALLERO REGOLARITA 50',
        '973': 'RR ENDURO 125',
        '245': 'SPEED TRIPLE',
        '3226': 'Stilo',
        '3906': 'Pixo',
        '1646': 'SFV GLADIUS 650',
        '3420': 'Focus 2/Focus C-Max',
        '3809': 'Nemo',
        '4211': '308 2ª serie',
        '1211': 'SL SHIVER 750',
        '3176': 'VALICO 500',
        '3170': '307',
        '5738': '',
        '3828': 'Picanto 2ª serie',
        '4430': 'Vito (4ª serie)',
        '277': 'NEO S',
        '3155': 'FELSBERG 125',
        '3376': 'Fiorino',
        '301': 'XT 660',
        '5622': 'SPEED 400',
        '3570': '107',
        '3572': 'Passat 6ª serie',
        '825': 'XL 650 V TRANSALP',
        '3505': 'Musa',
        '3780': '308 1ª serie',
        '150': 'PHANTOM 50',
        '4504': 'Crossland X',
        '4190': '2008',
        '4687': 'Yaris 4ª serie',
        '3457': 'X3 (E83)',
        '496': 'SPORTSTER 1200',
        '3388': 'A3 2ª serie',
        '897': 'MITO 125',
        '3676': 'S-Max',
        '1113': 'R 1200 R',
        '4168': 'Sandero 2ª serie',
        '4743': 'i20 3ª serie',
        '3770': 'Twingo 2ª serie',
        '2459': 'STREETZONE',
        '3370': 'Micra 3ª serie',
        '4575': 'A1 2ª serie',
        '4800': 'Serie 4 G.C. (G26)',
        '876': 'TYPHOON',
        '945': 'VESPA 125 LX',
        '2748': 'CROX 50',
        '3097': 'MP3 300 HPE',
        '3468': 'A6 3ª serie',
        '3147': '147',
        '3786': 'Mazda2 2ª serie',
        '3863': 'Massif (2008-2011)',
        '119': 'PEOPLE 50',
        '3579': 'RR Sport 1ª serie',
        '4156': 'Transit Custom',
        '4137': 'GT86',
        '4258': 'Serie 4 G.C. (F36)',
        '4122': 'V40',
        '4061': 'Daily (2011-2014)',
        '3888': 'Classe E (W/S212)',
        '3020': 'C 400 GT',
        '3626': 'Q7',
        '163': 'ELYSTAR 125',
        '3352': 'Fusion',
        '4144': 'Clio 4ª serie',
        '3415': 'Panda 2ª serie',
        '1623': 'BEVERLY 300',
        '3011': 'Seicento',
        '3946': 'DS3',
        '2967': 'R 1250 GS',
        '3854': 'MiTo',
        '1715': 'DOWNTOWN 300I',
        '3956': 'Giulietta (2010)',
        '283': 'TDM 900',
        '4525': 'Stonic',
        '4338': 'Vitara (2015)',
        '771': 'CB 500',
        '3571': 'C1',
        '3513': 'Classe A (W/C169)',
        '4330': 'Corsa 5ª serie',
        '3864': 'C4 2ª serie',
        '3530': 'Atom 2',
        '4301': 'forfour 2ªs. (W453)',
        '5084': 'SFIDA SR4 400',
        '2845': 'Classe SLK (R170)',
        '4665': 'Captur 2ª serie',
        '1130': 'GEOPOLIS 250',
        '3152': 'Doblò',
        '291': 'V MAX 1200',
        '5087': 'MG3',
        '4043': 'Freemont',
        '3769': 'Fabia 2ª serie',
        '3279': '4 Runner/Hilux 2ª',
        '2225': 'VISION 110',
        '389': 'ALP 200',
        '2057': 'TWEET 50',
        '661': 'R 1150 R',
        '4486': 'Rio 4ª serie',
        '1609': 'SW-T400',
        '1453': 'STELVIO 1200',
        '3040': 'Jimny',
        '4047': 'Master 5ª s.(10-->)',
        '4828': 'Range Rover 5ªserie',
        '856': 'BEVERLY 500',
        '3658': 'Note (2006-2013)',
        '3685': 'Daily (2006-2009)',
        '1164': 'PS 125',
        '911': 'VESPA 150 PX',
        '959': 'VESPA 250 GTS',
        '790': 'LEAD',
        '2409': 'Serie E (*124)',
        '2888': 'Ka 1ª serie',
        '2199': 'SR MAX 300',
        '3045': 'New Beetle',
        '3154': '',
        '3247': 'Porter 1ª/2ª serie',
        '2052': 'QUARTZ 50',
        '5096': '5 E-Tech Electric',
        '4342': 'XF 2ª serie (X260)',
        '3254': 'E4',
        '2413': '250 FREERIDE',
        '4236': 'Qashqai 2ª serie',
        '3439': 'Idea',
        '1900': 'MULTISTRADA 1200',
        '3789': 'Agila 2ª serie',
        '213': 'SV 650',
        '787': 'HORNET',
        '4024': 'Orlando',
        '2434': 'VESPA 50 PRIMAVERA',
        '4597': 'RR Evoque 2ª serie',
        '3763': 'C-Max 1ª serie',
        '537': 'Z 1000',
        '3721': 'NT 400/Cabstar 4ªs.',
        '3969': 'Meriva 2ª s.',
        '3283': 'Ibiza 3ª serie',
        '5391': 'T MAX 560',
        '4002': 'C-Max 2ª serie',
        '4498': 'Swift (2017--->)',
        '1173': 'X-CITY',
        '3242': 'XMF 125',
        '4326': 'Celerio',
        '5043': 'Tunland G7',
        '3540': 'CLASSIC 350',
        '2728': 'THRUXTON 1200',
        '5083': 'C3 4ª serie',
        '4702': 'Jazz 4ª serie \'20->',
        '2667': 'XSR 700',
        '3852': 'NP300',
        '4842': 'S-Cross 2ª serie',
        '5111': 'X3 (G45)',
        '3977': 'Juke',
        '3639': 'MX-5 3ª serie',
        '1196': 'FUOCO 500',
        '3221': 'Fiesta 4ª serie',
        '121': '2CV',
        '1138': 'CRE 50',
        '2242': 'BEVERLY 350',
        '3401': 'C2',
        '2518': 'MT-125',
        '2863': 'Marea',
        '912': 'VESPA 50 ET4',
        '4315': 'X6 (F16)',
        '1808': 'VFR 750 F',
        '354': 'SCARABEO 125',
        '4069': 'Fiesta 5ª serie Bs',
        '367': 'TUONO 1000',
        '2499': 'TWEET EVO 125',
        '2714': 'MEDLEY 125',
        '2915': 'Classe CLK (C/A208)',
        '4716': 'dr F35',
        '4233': 'Trans. Connect 2ª s',
        '3754': 'Bravo 2ª serie',
        '4610': 'T-Cross',
        '4282': 'Aygo 2ª serie',
        '3586': 'Classe B (T245)',
        '2902': 'Berlingo',
        '3968': 'A1/S1',
        '1208': 'SATELIS 400',
        '4595': 'Q3 2ª serie',
        '3517': 'C4',
        '4348': 'Karl',
        '2955': 'K-LIGHT 125',
        '3264': 'JET X 125',
        '4650': 'Defender (2019)',
        '2683': 'XSR 900',
        '116': 'PEOPLE 125',
        '4444': 'Q2',
        '3438': 'Golf 5ª serie',
        '4632': 'Serie 1 (F40)',
        '4555': '508 2ª serie',
        '2844': 'Elise',
        '322': 'YZF R1',
        '1212': 'HYPERMOTARD 1100',
        '146': 'MADISON 200',
        '4859': 'Aygo X',
        '3980': 'Punto Evo',
        '3628': 'Classe R (BR251)',
        '891': 'ZIP',
        '3653': 'RAV4 3ª serie',
        '819': 'VT 750 SHADOW',
        '3495': 'Serie 1 (E87)',
        '3128': '',
        '1464': 'MONSTER 696',
        '139': 'F 10',
        '944': 'VESPA 50 LX',
        '4721': '500 (2020--->)',
        '2633': 'DOWNTOWN 350I',
        '2323': 'VERSYS 1000',
        '4398': 'Sportage 4ª serie',
        '4242': 'Macan',
        '4134': 'Classe A (W176)',
        '4553': 'X4 (G02)',
        '4065': 'Classe B(T246/T242)',
        '697': 'MONSTER S4',
        '3826': 'C5 3ª serie',
        '3799': 'Tiguan',
        '2489': '1290 SUPER DUKE',
        '328': 'ATLANTIC 500',
        '487': 'DYNA SUPER GLIDE',
        '5062': 'Ypsilon 4ª serie',
        '2276': '1199 PANIGALE',
        '3521': 'Modus 1ª serie',
        '2435': 'VESPA 125 PRIMAVERA',
        '4360': '500 (2015--->)',
        '3265': 'MAXSYM TL 508',
        '4146': 'A3 3ª serie/S3',
        '3398': 'Punto 3ª serie',
        '3529': '147 2ª serie',
        '4142': '500L',
        '1213': 'STREET TRIPLE',
        '2341': '390 DUKE',
        '4359': 'Touran 3ª serie',
        '3861': 'Scirocco 2ª serie',
        '3010': 'Astra 2ª serie',
        '4974': 'Kona 2ªs. (2023-->)',
        '4491': 'Compass 2ª serie',
        '3384': 'Kangoo 2ª serie',
        '3391': 'Master 2ª serie',
        '3257': 'Combo (Corsa 3ª s.)',
        '3386': 'Meriva 1ª s.',
        '2715': 'MEDLEY 150',
        '4210': '500L Living',
        '3360': 'Daily (1992-1996)',
        '910': 'VESPA 125 PX',
        '4446': 'A5/S5 2a serie',
        '1639': 'Beta Berlina',
        '3266': 'CMX 1100 REBEL',
        '263': 'FZ6',
        '3859': 'Classe GLK (X204)',
        '1221': 'VESPA 125 S',
        '4178': 'Serie 3 G.T. (F34)',
        '4693': 'JOYRIDE 300',
        '3107': '890 DUKE',
        '4520': 'Arona',
        '4073': 'Mondeo 3ª serie Bs',
        '784': 'FORESIGHT',
        '3929': 'Astra 4ª serie',
        '855': 'BEVERLY 250',
        '4344': 'Q7 2ª serie',
        '3844': 'Ibiza 4ª serie',
        '1200': 'XEVO 250',
        '3599': 'Gr. Cherokee 2ª s.',
        '2990': 'Freelander 1ª serie',
        '4651': 'Korando 4ª serie',
        '1579': 'JOY MAX 300',
        '1613': 'MX-5 1ª serie',
        '5877': 'SRT 552',
        '3204': 'X-Trail 1ª serie',
        '3431': 'Picanto 1ª serie',
        '3902': 'Alto (2009--->)',
        '2849': 'CR-V 1ª serie 97-02',
        '3587': 'Fox',
        '3028': 'city coupé/cabrio',
        '866': 'LIBERTY 50',
        '4494': 'XC60 (2017--->)',
        '4534': 'DS7',
        '4668': 'Golf 8ª serie',
        '4362': 'Classe GLC (X253)',
        '3618': 'Grande Punto',
        '4230': 'Mini (F56)',
        '4500': 'Fiesta 6ª serie',
        '2716': 'ES2',
        '4150': 'Tourneo Custom',
        '4390': 'Tipo (2015--->)',
        '4770': 'Arkana',
        '5073': 'Swift (2024-->)',
        '3896': 'C3 Picasso',
        '1157': 'VERSYS 650',
        '2111': 'Cinquecento',
        '3096': 'Agila 1ª serie',
        '1220': 'VESPA 50 S',
        '1004': 'SILVER WING 400',
        '2309': 'BRUTALE 675',
        '2735': 'TR 50 SM',
        '3839': 'Sandero 1ª serie',
        '772': 'CBF 500',
        '3677': 'Caliber',
        '476': 'NEXUS 500',
        '4940': 'dr 3.0',
        '817': 'VT 600 SHADOW',
        '4426': 'GIULIA (2016) 2.9 T V6 AT8 QUADRIFOGLIO',
        '2823': 'CRUISYM 300',
        '4314': 'e-Golf',
        '3862': 'Serie 7 (F01/02/04)',
        '3425': 'Astra 3ª serie',
        '4276': 'C4 Cactus',
        '2641': 'NMAX 125',
        '3650': 'Sedici',
        '5926': '450 MT',
        '3918': 'X1 (E84)',
        '1070': 'FLY 100',
        '3830': 'i10 1ª serie',
        '3934': '5008',
        '1174': 'PHANTOM F12-R 50',
        '3308': 'Ducato (3ª serie)',
        '3979': 'Mini Countryman R60',
        '5136': 'T03',
        '883': 'VESPA 50 ET2',
        '462': '4',
        '2359': 'CBR 500',
        '3276': 'Daily (1999-2007)',
        '3800': 'Serie 1 Cabrio(E88)',
        '2142': 'K 1600 GTL',
        '3920': 'Punto Evo',
        '3498': 'H1',
        '2688': 'CRF1000L AFRICA TWIN',
        '4053': 'Q3',
        '3992': 'X3 (F25)',
        '4186': 'Captur',
        '3337': '9-3 2ª serie',
        '4588': 'Serie 3 (G20)',
        '2': 'Panda',
        '4753': 'Sandero 3ª serie',
        '4376': 'A4 5ª serie',
        '1184': 'CARNABY 125',
        '1031': 'LIBERTY S 200',
        '4991': 'ZR-V',
        '4009': 'ix20',
        '3869': 'Golf 6ª serie',
        '4325': 'i20 2ª serie',
        '4182': 'RAV4 4ª serie',
        '3970': 'Master 5ª serie',
        '293': 'WHY',
        '3964': 'Duster',
        '3008': 'Clio 2ª serie',
        '3883': 'iQ',
        '4437': 'Ka+',
        '3740': 'Scudo',
        '4205': 'C4 Picasso',
        '5057': 'e-C3',
        '2056': 'PCX 125',
        '4227': 'i10 2ª serie',
        '3066': 'LEONCINO 250',
        '4586': 'ceed',
        '337': 'LEONARDO 250',
        '1626': 'MOTARD 125',
        '4820': 'Taigo',
        '854': 'BEVERLY 200',
        '681': '749',
        '3333': 'V 435',
        '4765': 'EQA (H243)',
        '4170': 'Adam',
        '3231': 'FORZA 750',
        '3076': 'Strada',
        '3174': 'Trans/Tour/Bus 2000',
        '2786': 'MULTISTRADA 950',
        '4797': 'Bayon',
        '1625': 'MOTARD 50',
        '1602': 'AGILITY 150',
        '255': 'BT 1100',
        '1065': 'SX 50',
        '3243': 'TIGER 850',
        '4292': 'Passat 8ª serie',
        '666': 'R 1200 GS',
        '3581': 'Swift (2005-2010)',
        '3246': 'SH 350',
        '4831': 'CB 750',
        '3771': 'Mondeo 3ª serie',
        '4875': 'dr 6.0',
        '312': 'XVS 650A DRAG STAR',
        '2771': 'NMAX 155',
        '3125': 'Classe C (W/S203)',
        '249': 'TIGER',
        '2384': 'Ibiza 2ª serie',
        '': '', // Empty code
        '4299': 'fortwo 3ª s. (C453)',
        '273': 'MAJESTY 400',
        '4569': 'Q8',
        '4357': 'Tivoli',
        '4455': 'C3 3ª serie',
        '4683': 'Classe GLA (H247)',
        '3609': 'Clio 3ª serie',
        '3311': 'CR-V 2ª serie 02-07',
        '2797': 'AGILITY 150I',
        '869': 'LIBERTY 200',
        '2058': 'TWEET 125',
        '4256': 'Soul 2ª serie',
        '3259': 'Polo 4ª serie',
        '880': 'VESPA 150 ET4',
        '4552': 'Classe A (W177)',
        '3880': 'QUBO',
        '3591': 'Matiz 2ª serie',
        '4229': 'Tour. Connect 2ª s.',
        '3990': 'Touran 2ª serie',
        '4341': 'Outback 4ª serie',
        '4526': 'XC40 (2017--->)',
        '4746': 'VESPA 50 SPECIAL (V5B3)',
        '660': 'R 1150 GS',
        '3399': 'Serie 5 (E60/E61)',
        '3140': 'T MAX 560',
        '2390': 'Terrano II',
        '1126': 'MULTISTRADA 1100',
        '2456': 'R NINET',
        '4005': 'Micra 4ª serie',
        '2201': 'Ducato (2ª serie)',
        '902': 'ATLANTIC 200',
        '4068': 'C-Max 2ª serie Bs',
        '562': 'SKYLINER 250',
        '3803': 'Fiorino 2ª serie',
        '4524': 'T-Roc',
        '3343': 'Mégane 2ª serie',
        '997': 'MT 03',
        '996': 'X-MAX 125',
        '3693': 'Corsa 4ª serie',
        '4074': 'Ka 2ª serie Bs',
        '2473': 'MT-07',
        '4623': 'Classe CLA Coupé',
        '5044': 'VESPA PK 50',
        '147': 'MADISON 250',
        '4070': 'Focus 4ª serie Bs',
        '3712': 'C30 (2006-2012)',
        '974': 'K 1200 R',
        '4767': 'Qashqai 3ª serie',
        '4004': 'Passat 7ª serie',
        '4825': 'Astra 6ª serie',
        '2587': 'CB 125',
        '729': 'NEVADA 750',
        '4311': 'Renegade',
        '3642': 'Yaris 2ª serie',
        '3865': 'Fiesta 5ª serie',
        '4123': 'Mii',
        '3837': 'Berlingo 2ª serie',
        '4654': 'Classe GLB (X247)',
        '4458': 'Mini Countryman F60',
        '1501': 'SPORTCITY 300',
        '191': 'AN BURGMAN 250',
        '4613': 'S60 (2019-->)',
        '6020': 'HIMALAYAN 450',
        '4326': 'Fabia 3ª serie',
        '4472': 'Ignis (2016)',
        '3237': 'MULTISTRADA V4',
        '2393': 'X-MAX 400',
        '1528': 'Feroza',
        '2789': 'STREET SCRAMBLER',
        '2098': 'Serena',
        '2787': 'Z 650',
        '3756': 'Serie 1 (E81)',
        '204': 'GSX 750',
        '3027': '206',
        '4652': 'Puma',
        '174': 'Y10',
        '4611': 'Model 3',
        '3996': 'Swift (2010--->)',
        '2691': 'STREET TWIN',
        '5009': 'Tourneo Courier 2ªs',
        '3659': 'SX4',
        '4289': 'NX 1ª serie',
        '5717': 'SFIDA SR1 125',
        '395': 'EURO 350',
        '3036': 'Focus 1ª serie',
        '4473': 'Micra 5ª serie',
        '3241': 'C3 1ª serie',
        '4501': 'Ibiza 5ª serie',
        '813': 'VFR',
        '4313': 'Panda Cross',
        '123': 'VITALITY 50',
        '4368': 'Jazz 3ª serie \'15->',
        '3903': 'Soul 1ª serie',
        '3824': 'Splash',
        '207': 'GSX R 600',
        '4485': 'Range Rover Velar',
        '4471': 'Kodiaq',
        '1399': 'SOFTAIL CROSS BONES',
        '4022': 'Amarok',
        '4290': 'Vivaro 3ª serie',
        '4475': 'Stelvio',
        '3602': 'Zafira 2ª serie',
        '2588': 'DJANGO 125',
        '2917': 'Classe A (W/V168)',
        '4329': 'Mazda2 3ª serie',
        '2953': 'BN 125',
        '595': 'SMR 450 F'
    };

    return MODEL_CODES[modelCode] || '';
  }

  getPaymentMethodFromCode(paymentMethodCode) {
    const PAYMENT_METHOD_CODES = {
        'A': 'ASSEGNO',
        'B': 'BANCA TITOLO SINGOLO',
        'C': 'CONTANTE',
        'D': 'INCASSO DIREZIONE',
        'F': 'FINANZIAMENTO FINITALIA',
        'H': 'PAGAMENTO MULTICANALITà (CARTA DI CREDITO)',
        'J': 'POS AGENZIA',
        'K': 'REINVESTIMENTO',
        'L': 'LINK',
        'M': 'MISTO',
        'P': 'POSTA',
        'Q': 'ANTICIPAZIONE COPERTURA AGENTI',
        'R': 'BANCA/POSTA',
        'T': 'TRATTENUTA MENSILE CONVENZIONI TM00',
        'V': 'COPERTURA SENZA INCASSO',
        'W': 'BANCA TITOLI MULTIPLI',
        'X': 'TRATTENUTA MENSILE COMMISSIONI GCF',
        'Y': 'POS DIREZIONE'
    };

    return PAYMENT_METHOD_CODES[paymentMethodCode] || '';
  }

  getProductFromCode(productCode) {
    const PRODUCT_CODES = {
        '9080': 'PRODOTTO UNICO AUTO  RCA / ARD',
        '7301': 'CANE & GATTO',
        '7505': 'PRODOTTO UNICO MOBILITÀ',
        '9081': 'PRODOTTO UNICO AUTO  ARD',
        '1206': 'INFORTUNI',
        '1268': 'SALUTE',
        '7267': 'CASA',
        '2215': 'PRODOTTO UNICO VIAGGI TEMPORANEO',
        '7266': 'FAMIGLIA'
    };

    return PRODUCT_CODES[productCode] || '';
}

  formatDate = (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('it-IT', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    };

  // Permission check method
  checkPermission(type) {
    switch (type) {
      case "visualizzazione":
        return hasVisualizzazionePermission1 || hasVisualizzazionePermission2;
      case "variazione":
        return (
          hasVariazionePermission1 ||
          hasVariazionePermission2 ||
          hasVariazionePermission3 ||
          hasVariazionePermission4 ||
          hasVariazionePermission5 ||
          hasVariazionePermission6 ||
          hasVariazionePermission7 ||
          hasVariazionePermission8 ||
          hasVariazionePermission9 ||
          hasVariazionePermission10 ||
          hasVariazionePermission11 ||
          hasVariazionePermission12 ||
          hasVariazionePermission13 ||
          hasVariazionePermission14 ||
          hasVariazionePermission15 ||
          hasVariazionePermission16 ||
          hasVariazionePermission17 ||
          hasVariazionePermission18 ||
          hasVariazionePermission19 ||
          hasVariazionePermission20 ||
          hasVariazionePermission21 ||
          hasVariazionePermission22 ||
          hasVariazionePermission23 ||
          hasVariazionePermission24 ||
          hasVariazionePermission25 ||
          hasVariazionePermission26 ||
          hasVariazionePermission27 ||
          hasVariazionePermission28 ||
          hasVariazionePermission29 ||
          hasVariazionePermission30 ||
          hasVariazionePermission31 ||
          hasVariazionePermission32 ||
          hasVariazionePermission33 ||
          hasVariazionePermission34 ||
          hasVariazionePermission35 ||
          hasVariazionePermission36 ||
          hasVariazionePermission37 ||
          hasVariazionePermission38 ||
          hasVariazionePermission39 ||
          hasVariazionePermission40 ||
          hasVariazionePermission41 ||
          hasVariazionePermission42 ||
          hasVariazionePermission43 ||
          hasVariazionePermission44 ||
          hasVariazionePermission45
        );
      case "sostituzione":
        return (
          hasSostituzionePermission1 ||
          hasSostituzionePermission2 ||
          hasSostituzionePermission3 ||
          hasSostituzionePermission4 ||
          hasSostituzionePermission5 ||
          hasSostituzionePermission6 ||
          hasSostituzionePermission7 ||
          hasSostituzionePermission8 ||
          hasSostituzionePermission9 ||
          hasSostituzionePermission10 ||
          hasSostituzionePermission11 ||
          hasSostituzionePermission12
        );
      case "apertura_sinistro":
        return hasCustomPermission;
      default:
        return false;
    }
  }

  // Getters for template use
  get hasVisualizzazionePermission() {
    return this.checkPermission("visualizzazione");
  }

  get hasVariazionePermission() {
    return this.checkPermission("variazione");
  }

  get hasSostituzionePermission() {
    return this.checkPermission("sostituzione");
  }

  get hasAperturaSinistroPermission() {
    return this.checkPermission("apertura_sinistro");
  }

  @wire(CurrentPageReference)
  getStateParameters(currentPageReference) {
    if (currentPageReference && currentPageReference.state) {
      const agency = currentPageReference.state.c__agency;
      if (agency === 'other') {
        this.agencyType = 'other';
      } else {
        this.agencyType = 'same';
      }
    }
  }

  get agencyTypeIsOther() {
    return this.agencyType === 'other';
  }

  get isSameUserAgency() {
    return this.agencyType === 'same';
  }

}