/**
 * @File Name         : OpportunityManagementWS.cls
 * @Description       : 
 * <AUTHOR> <EMAIL>
 * @Group             : 
 * @Last Modified On  : 22-03-2025
 * @Last Modified By  : <EMAIL>
@cicd_tests OpportunityUtilsTest
**/
global without sharing class OmnistudioUtils implements System.Callable {
    private static final String MAIL_CONTACT_TYPE = 'MAIL';
    private static final String CELL_CONTACT_TYPE = 'CELL';
    private static final String TEL_CONTACT_TYPE = 'TEL';
    private static final String TELUFF_CONTACT_TYPE = 'TELUFF';
    private static final String TELREF_CONTACT_TYPE = 'TELREF';
    private static final String PEC_CONTACT_TYPE = 'PEC';
    private static final String FAX_CONTACT_TYPE = 'FAX';

    private static final String INSERT_INPUT_MODE = 'INSERT';
    private static final String UPDATE_INPUT_MODE = 'UPDATE';

    private static final String GENERIC_ERROR = 'Si è verificato un errore';
    private static final String REQUIRED_ERROR = 'I campi obbligatori non sono stati compilati';
    private static final String VALIDATION_ERROR = 'Il recapito inserito non è valido';
    private static final String SITO_ERROR = 'Il sito web inserito non è valido';
    private static final String CONTACT_UPDATE_DM = 'UpdateRecapiti';

    private static final Map<String, String> OMOCODIA = new Map<String, String>{ 'L' => '0', 'M' => '1', 'N' => '2', 'P' => '3', 'Q' => '4', 'R' => '5', 'S' => '6', 'T' => '7', 'U' => '8', 'V' => '9' };

    public Object call(String action, Map<String, Object> args) {
        Map<String, Object> input = (Map<String, Object>) args.get('input');
        Map<String, Object> output = (Map<String, Object>) args.get('output');
        Map<String, Object> options = (Map<String, Object>) args.get('options');

        Object result = invokeMethod(action, input, output);
        System.debug('///Result: ' + result);

        return result;
    }

    public Boolean invokeMethod(String methodName, Map<String, Object> inputs, Map<String, Object> output) {
        Boolean result = true;
        try {
            if (methodName.equals('validateContact')) {
                validateContact(inputs, output);
            } else if (methodName.equals('getAgencyCode')) {
                getAgencyCode(inputs, output);
            } else if (methodName.equals('updateObject')) {
                updateObject(inputs, output);
            } else if (methodName.equals('validateWebsite')) {
                validateWebsite(inputs, output);
            } else if (methodName.equals('retrieveKpiAnag')) {
                retrieveKPI(inputs, output);
            } else if (methodName.equals('upsertKpiAnag')) {
                upsertKPI(inputs, output);
            } else if (methodName.equals('setDatiSocioEconomiciRequestBody_datiAttributi')) {
                setDatiSocioEconomiciRequestBody_datiAttributi(inputs, output);
            } else if (methodName.equals('checkCustomPermissions')) {
                checkCustomPermissions(inputs, output);
            } else if (methodName.equals('checkAbbinato')) {
                checkAbbinato(inputs, output);
            } else if (methodName.equals('KPIAnag')) {
                retrieveKPIAnag(inputs, output);
            } else if (methodName.equals('retrieveKPIbyKey')) {
                retrieveKPIbyKey(inputs, output);
            } else if (methodName.equals('getStatoComuneCF')) {
                getStatoComuneCF(inputs, output);
            } else if (methodName.equals('stringManipulation')) {
                stringManipulation(inputs, output);
            } else if (methodName.equals('checkClienteAltraAgenzia')) {
                checkClienteAltraAgenzia(inputs, output);
            } else {
                output.put('success', false);
                output.put('errorMessage', 'Metodo non riconosciuto: ' + methodName);
                result = false;
            }
        } catch (Exception e) {
            output.put('success', false);
            output.put('errorMessage', GENERIC_ERROR);
            result = false;
        }

        return result;
    }

    private void getAgencyCode(Map<String, Object> inputs, Map<String, Object> output) {
        String userId = (String) inputs.get('userId');

        User targetUser;
        if (Schema.sObjectType.User.fields.IdAzienda__c.isAccessible()) {
            targetUser = [SELECT Id, IdAzienda__c FROM User WHERE Id = :userId LIMIT 1];
        } else {
            output.put('success', false);
            output.put('errorMessage', GENERIC_ERROR);
            return;
        }

        if (targetUser != null) {
            Account agency;
            if (Schema.sObjectType.Account.fields.AgencyCode__c.isAccessible()) {
                agency = [SELECT Id, AgencyCode__c FROM Account WHERE Id = :targetUser.IdAzienda__c LIMIT 1];
            } else {
                output.put('success', false);
                output.put('errorMessage', GENERIC_ERROR);
                return;
            }

            output.put('success', true);
            output.put('result', agency.AgencyCode__c);
        } else {
            output.put('success', false);
            output.put('errorMessage', GENERIC_ERROR);
        }
    }

    private void validateContact(Map<String, Object> inputs, Map<String, Object> output) {
        String contactType = (String) inputs.get('contactType');
        String contactValue = ((String) inputs.get('contactValue')?.toString())?.replaceAll(' ', '');
        String contactTarget = (String) inputs.get('contactTarget');

        if (!checkRequiredFields(inputs)) {
            output.put('success', false);
            output.put('result', contactValue);
            output.put('errorMessage', REQUIRED_ERROR);
            return;
        }

        Boolean isCell = contactType == CELL_CONTACT_TYPE;
        Boolean isEmail = contactType == MAIL_CONTACT_TYPE || contactType == PEC_CONTACT_TYPE;
        Boolean isPhone = contactType == TEL_CONTACT_TYPE || contactType == TELUFF_CONTACT_TYPE || contactType == TELREF_CONTACT_TYPE || contactType == FAX_CONTACT_TYPE;

        if (isEmail) {
            contactValue = contactValue.toLowerCase();
            Pattern emailPattern = Pattern.compile('^[A-Za-z0-9._-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$');
            Matcher matcher = emailPattern.matcher(contactValue);
            output.put('success', matcher.matches());
        } else if (isPhone || isCell) {
            contactValue = contactValue.replaceAll('[^0-9+]', '');
            if (contactValue.startsWith('00')) {
                contactValue = '+' + contactValue.substring(2);
            }
            Pattern phonePattern = isPhone ? Pattern.compile('^(\\+)?\\d{6,14}$') : Pattern.compile('^(\\+)?\\d{9,14}$');
            Matcher matcher = phonePattern.matcher(contactValue);
            Boolean isValid = matcher.matches();
            if (isValid) {
                // Check if all characters are the same digit
                Set<String> uniqueChars = new Set<String>();
                for (Integer i = 0; i < contactValue.length(); i++) {
                    uniqueChars.add(contactValue.substring(i, i + 1));
                }
                isValid = uniqueChars.size() > 1;
            }
            output.put('success', isValid);
        } else {
            output.put('success', false);
        }

        if (!((Boolean) output.get('success'))) {
            output.put('errorMessage', VALIDATION_ERROR);
        }

        output.put('result', contactValue);
    }

    private Boolean checkRequiredFields(Map<String, Object> inputs) {
        String contactType = (String) inputs.get('contactType');
        String contactValue = (String) (inputs.get('contactValue')?.toString());
        String contactTarget = (String) inputs.get('contactTarget');
        String contactPerson = (String) inputs.get('contactPerson');
        String inputMode = (String) inputs.get('inputMode');

        if (inputMode != INSERT_INPUT_MODE && inputMode != UPDATE_INPUT_MODE) {
            return false;
        }

        Boolean refValid = contactType != TELREF_CONTACT_TYPE || (contactType == TELREF_CONTACT_TYPE && contactPerson != null && contactPerson != '');

        return !(contactType == null || contactType == '' || contactValue == null || contactValue == '' || ((contactTarget == null || contactTarget == '') && inputMode == INSERT_INPUT_MODE) || !refValid);
    }

    private void updateObject(Map<String, Object> inputs, Map<String, Object> output) {
        String objectApiName = (String) inputs.get('objectApiName');
        Map<String, Object> fields = (Map<String, Object>) inputs.get('fields');

        SObject record = (SObject) Type.forName('Schema.' + objectApiName).newInstance();
        for (String key : fields.keySet()) {
            record.put(key, fields.get(key));
        }

        try {
            if (record != null) {
                update record;
                output.put('success', true);
            } else {
                output.put('success', false);
                output.put('errorMessage', GENERIC_ERROR);
            }
        } catch (Exception e) {
            System.debug(':::updateObject error: ' + e.getMessage());
            output.put('success', false);
            output.put('errorMessage', GENERIC_ERROR);
        }
    }

    private void validateWebsite(Map<String, Object> inputs, Map<String, Object> output) {
        String sitoWeb = (String) inputs.get('sito');
        /*if(String.isBlank(sitoWeb)){
            output.put('success', false);
            output.put('result', sitoWeb);
            output.put('errorMessage', REQUIRED_ERROR);
            return;
        }*/
        String urlPattern = '^(https?://)?([\\da-z.-]+)\\.([a-z.]{2,6})([/\\w .-]*)*/?$';
        Boolean isValid = Pattern.matches(urlPattern, sitoWeb);
        output.put('success', isValid);
        if (!((Boolean) output.get('success'))) {
            output.put('errorMessage', SITO_ERROR);
        }
        output.put('result', sitoWeb);
    }

    /* private void retrieveKPIByDataRaptor(String bundleName, String jsonString){
    	//String bundleName = 'GetSocietyAccountAccountRelationByAccount';
        Map<String, Object> objectList = new Map<String, Object>{'RelatedId'=>'MyValue'};
        //String jsonString = JSON.serialize(objectList);
        List<String> jsonInputData = new List<String>();
        jsonInputData.add(jsonString);
        ConnectApi.DataMapperExecuteInputRepresentation apexInput = new ConnectApi.DataMapperExecuteInputRepresentation();
        apexInput.dataMapperInput = jsonInputData;
        apexInput.inputType = 'JSON';
        ConnectApi.DataMapperExecuteOptionsRepresentation options = new ConnectApi.DataMapperExecuteOptionsRepresentation();
        options.locale = null;
        options.shouldSendLegacyResponse = true;
        apexInput.options = options;
        ConnectApi.DataMapperExecuteOutputRepresentation output = ConnectApi.OmniDesignerConnect.executeDataMapper(bundleName, apexInput);
        
       
        List<String> innerResponse = output.response;
        for (String currentResponse : innerResponse){
            System.debug(JSON.serialize(currentResponse));
        	Map<String, Object> outerMap = (Map<String, Object>) JSON.deserializeUntyped(currentResponse);  
        }
    } */

    private void retrieveKPIbyKey(Map<String, Object> inputs, Map<String, Object> output) {
        String aarSociety = (String) inputs.get('aarSociety');
        String aarAgency = (String) inputs.get('aarAgency');
        String keyFilter = (String) inputs.get('key__c'); // Nuovo input

        List<Map<String, Object>> kpiListMap = new List<Map<String, Object>>();

        if (String.isNotBlank(aarSociety)) {
            List<Asset> kpiSocietyList = [
                SELECT Id, Key__c, Name, SourceSystem__c, Value__c, MasterRecordId__c, SourceSystemLastUpdateDate__c
                FROM Asset
                WHERE MasterRecordId__c = :aarSociety AND Key__c = :keyFilter
            ];
            for (Asset a : kpiSocietyList) {
                kpiListMap.add(new Map<String, String>{ 'key' => a.Key__c, 'value' => a.Value__c });
            }
        }

        if (String.isNotBlank(aarAgency)) {
            List<Asset> kpiAgencyList = [
                SELECT Id, Key__c, Name, SourceSystem__c, Value__c, MasterRecordId__c, SourceSystemLastUpdateDate__c
                FROM Asset
                WHERE MasterRecordId__c = :aarAgency AND Key__c = :keyFilter
            ];
            for (Asset a : kpiAgencyList) {
                kpiListMap.add(new Map<String, String>{ 'key' => a.Key__c, 'value' => a.Value__c });
            }
        }

        output.put('kpiList', kpiListMap);
    }

    //Guy De Roquefeuil: refactored retrieveKPI
    private void retrieveKPI(Map<String, Object> inputs, Map<String, Object> output) {
        String aarSociety = (String) inputs.get('aarSociety');
        String aarAgency = (String) inputs.get('aarAgency');
        List<Map<String, Object>> kpiListMap = new List<Map<String, Object>>();
        if (String.isBlank(aarSociety) == false) {
            List<Asset> kpiSocietyList = [
                SELECT Id, Key__c, Name, SourceSystem__c, Value__c, MasterRecordId__c, SourceSystemLastUpdateDate__c
                FROM Asset
                WHERE MasterRecordId__c = :aarSociety
            ];
            for (Asset a : kpiSocietyList) {
                kpiListMap.add(new Map<String, String>{ 'key' => a.Key__c, 'value' => a.Value__c });
            }
        }
        if (String.isBlank(aarAgency) == false) {
            List<Asset> kpiAgencyList = [
                SELECT Id, Key__c, Name, SourceSystem__c, Value__c, MasterRecordId__c, SourceSystemLastUpdateDate__c
                FROM Asset
                WHERE MasterRecordId__c = :aarAgency
            ];
            for (Asset a : kpiAgencyList) {
                kpiListMap.add(new Map<String, String>{ 'key' => a.Key__c, 'value' => a.Value__c });
            }
        }

        output.put('kpiList', kpiListMap);
    }

    private void retrieveKPIAnag(Map<String, Object> inputs, Map<String, Object> output) {
        String aarSociety = (String) inputs.get('aarSociety');
        String aarAgency = (String) inputs.get('aarAgency');
        //List<Map<String, Object>> accountKey = new List<Map<String, Object>>();
        //List<Map<String, Object>> accountKeyAgency = new List<Map<String, Object>>();
        Map<String, Object> accountKey = new Map<String, Object>();
        Map<String, Object> accountKeyAgency = new Map<String, Object>();
        Pattern datePattern = Pattern.compile('((?:19|20)\\d\\d)-(0?[1-9]|1[012])-([12][0-9]|3[01]|0?[1-9])');
        System.debug('@AP' + aarSociety);
        if (!String.isBlank(aarSociety)) {
            List<Asset> kpiSocietyList = [
                SELECT Id, Key__c, Name, SourceSystem__c, Value__c, MasterRecordId__c, SourceSystemLastUpdateDate__c
                FROM Asset
                WHERE MasterRecordId__c = :aarSociety
            ];
            String aarSValue;
            System.debug('@AP' + kpiSocietyList);
            for (Asset a : kpiSocietyList) {
                if (a.Key__c == 'CRMA_CAPACITADISPESA') {
                    System.debug('@AP' + 'CRMA_CAPACITADISPESA');
                    switch on a.Value__c {
                        when 'Basso' {
                            aarSValue = 'Bassa';
                        }
                        when 'Medio' {
                            aarSValue = 'Media';
                        }
                        when 'Alto' {
                            aarSValue = 'Alta';
                        }
                        when else {
                            aarSValue = a.Value__c;
                        }
                    }
                } else if (a.Key__c == 'CRMA_VALUS') {
                    System.debug('@AP' + 'CRMA_VALUS');
                    switch on a.Value__c {
                        when 'a. Molto Basso' {
                            aarSValue = '1';
                        }
                        when 'b. Basso' {
                            aarSValue = '2';
                        }
                        when 'c. Medio' {
                            aarSValue = '3';
                        }
                        when 'd. Alto' {
                            aarSValue = '4';
                        }
                        when 'e. Molto Alto' {
                            aarSValue = '5';
                        }
                        when else {
                            aarSValue = a.Value__c;
                        }
                    }
                } else if (a.Key__c == 'CRMA_NEXTBESTOFFERING') {
                    system.debug('@AP ' + a.Value__c);
                    String value = a.Value__c;
                    value = value.substringAfter('DS_MACROBISOGNO\':\'');
                    value = value.substringBefore('\',\'FL_CUTOFF\'');
                    value = value.substringAfter(' ');
                    aarSValue = value;
                } else if (a.Key__c == 'TPD_DATAULTIMOACCESSO') {
                    System.debug('@AP' + 'TPD_DATAULTIMOACCESSO');
                    DateTime dt = (datePattern.matcher(a.Value__c.left(10)).matches()) ? DateTime.valueOf(a.Value__c) : null;
                    aarSValue = dt?.format('dd/MM/yyyy');
                } else if (a.Key__c == 'TPD_PRESENZAAPP') {
                    aarSValue = a.Value__c;
                    switch on a.Value__c.toLowerCase() {
                        when 'true' {
                            aarSValue = 'Sì';
                        }
                        when else {
                            aarSValue = 'No';
                        }
                    }
                } else {
                    aarSValue = a.Value__c;
                }
                accountKey.put(a.Key__c, new Map<String, String>{ 'Value' => aarSValue });
            }
        }
        if (!String.isBlank(aarAgency)) {
            List<Asset> kpiAgencyList = [
                SELECT Id, Key__c, Name, SourceSystem__c, Value__c, MasterRecordId__c, SourceSystemLastUpdateDate__c
                FROM Asset
                WHERE MasterRecordId__c = :aarAgency
            ];
            String aarAValue;
            for (Asset a : kpiAgencyList) {
                if (a.Key__c == 'CRM_CLIENTE_VIP' || a.Key__c == 'MDM_CONTATTABILITA') {
                    aarAValue = a.Value__c == 'true' ? 'SI' : 'NO';
                } else if (a.Key__c == 'PP_CONVENZIONI') {
                    system.debug('@AP ' + a.Value__c);
                    if (!String.isBlank(a.Value__c)) {
                        String value = a.Value__c;
                        value = value.replaceAll('\'', '"');
                        aarAValue = value;
                    }
                } else {
                    aarAValue = a.Value__c;
                }
                accountKeyAgency.put(a.key__c, new Map<String, String>{ 'Value' => aarAValue });
            }
        }
        Map<String, Object> kpiList = new Map<String, Object>{ 'AccountKey' => accountKey, 'AccountKeyAgency' => accountKeyAgency };
        output.put('KPI', kpiList);
    }

    private void checkCustomPermissions(Map<String, Object> inputs, Map<String, Object> output) {
        List<Object> customPermissionsObject = (List<Object>) inputs.get('customPermissions');
        List<String> customPermissions = new List<String>();
        for (Object cpo : customPermissionsObject) {
            customPermissions.add((String) cpo);
        }
        List<CustomPermission> cps = [
            SELECT id, DeveloperName
            FROM CustomPermission
            WHERE DeveloperName = :customPermissions
        ];
        Map<String, Object> customPermMap = new Map<String, Object>();
        Boolean anyPermission = false;
        Boolean allPermission = true;
        for (CustomPermission cp : cps) {
            Boolean check = FeatureManagement.checkPermission(cp.developerName);
            customPermMap.put(cp.developerName, check);
            if (check && !anyPermission) {
                anyPermission = true;
            } else if (!check && allPermission) {
                allPermission = false;
            }
        }

        output.put('customerPermissionCheck', customPermMap);
        output.put('anyPermission', anyPermission);
        output.put('allPermission', allPermission);
    }

    //Guy De Roquefeuil: added upsert KPI
    private void upsertKPI(Map<String, Object> inputs, Map<String, Object> output) {
        Map<String, Object> accountKPIMap = (Map<String, Object>) inputs.get('kpi_list_to_be_upserted');
        String societyAccountAccountRelationId = (String) inputs.get('society_account_account_relation_id');
        if (accountKPIMap == null || String.isBlank(societyAccountAccountRelationId))
            return;

        List<Asset> accountKPIToBeUpserted = new List<Asset>();
        Map<String, Asset> existingAccountKPIMap = new Map<String, Asset>();
        //TODO: to be bulkified for more accounts if needed
        for (Asset existingAccountKPI : [SELECT Id, Key__c, Value__c FROM Asset WHERE MasterRecordId__c = :societyAccountAccountRelationId LIMIT 50000])
            existingAccountKPIMap.put(existingAccountKPI.Key__c.toUpperCase(), existingAccountKPI);

        FinServ__AccountAccountRelation__c societyAccountAccountRelation = [SELECT Id, FinServ__Account__r.ExternalId__c, FinServ__RelatedAccount__r.Identifier__c FROM FinServ__AccountAccountRelation__c WHERE Id = :societyAccountAccountRelationId LIMIT 1].get(0);
        String accountExternalId = societyAccountAccountRelation.FinServ__Account__r.ExternalId__c;
        String societyIdentifier = societyAccountAccountRelation.FinServ__RelatedAccount__r.Identifier__c;
        for (String key : accountKPIMap.keySet()) {
            String upperCasedKey = key.toUpperCase(); //N.B. a more robust INsensitive check is appropriate here, hence the conversion

            if (existingAccountKPIMap.keySet().contains(upperCasedKey)) {
                Asset accountKPIToBeUpdated = existingAccountKPIMap.get(upperCasedKey);
                String newValue = String.valueOf(accountKPIMap.get(key));
                accountKPIToBeUpdated.Value__c = newValue;
                accountKPIToBeUpserted.add(accountKPIToBeUpdated);
                continue;
            }

            Asset newAccountKPI = new Asset(
                MasterRecordId__c = societyAccountAccountRelationId,
                Name = accountExternalId + '_' + societyIdentifier + '_' + 'ANAG2_DATI_SOCIO_ECONOMICI' + '_' + upperCasedKey, //SNCFNK80A01H501B_1_ANAG2_DATI_SOCIO_ECONOMICI_DATIATTRIBUTI
                ExternalId__c = accountExternalId + '_' + societyIdentifier + '_' + 'ANAG2_DATI_SOCIO_ECONOMICI' + '_' + upperCasedKey, //SNCFNK80A01H501B_1_ANAG2_DATI_SOCIO_ECONOMICI_DATIATTRIBUTI
                Key__c = upperCasedKey,
                Value__c = String.valueOf(accountKPIMap.get(key))
            );
            accountKPIToBeUpserted.add(newAccountKPI);
        }

        System.debug('about to upsert: ' + JSON.serialize(accountKPIToBeUpserted));
        if (inputs.get('disable_upsert') != null && (Boolean) inputs.get('disable_upsert') == true)
            return;

        try {
            upsert accountKPIToBeUpserted;
            output.put('apex_result', 'OK');
        } catch (Exception e) {
            output.put('apex_result', JSON.serialize(e));
        }
    }

    //Guy De Roquefeuil: added setDatiSocioEconomiciRequestBody_datiAttributi
    private void setDatiSocioEconomiciRequestBody_datiAttributi(Map<String, Object> inputs, Map<String, Object> output) {
        List<Map<String, String>> datiAttributi = new List<Map<String, String>>();
        Set<Object> datiAttributiSet = new Set<Object>((List<Object>) inputs.get('dati_attributi'));
        datiAttributiSet?.remove('');
        if (datiAttributiSet == null || datiAttributiSet.isEmpty()) {
            output.put('datiAttributi', datiAttributi);
            return;
        }

        for (Object attributo : datiAttributiSet)
            datiAttributi.add(new Map<String, String>{ 'attributo' => (String) attributo });

        output.put('datiAttributi', datiAttributi);
    }

    //# Raffaele Granato: Raffaele Granato | UNF-761 - Coni di visibilità - Scheda soggetto
    private void checkAbbinato(Map<String, Object> inputs, Map<String, Object> output) {
        String accountId = (String) inputs.get('accountId');
        //Id idAzienda = [SELECT IdAzienda__c FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1]?.IdAzienda__c;
        Id idAzienda = NetworkUserHelper.getAgencyIdFromNetworkUserId();
        Id idAccountAgency = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('AccountAgency').getRecordTypeId();
        Boolean isAbbinato = [
                SELECT Id
                FROM FinServ__AccountAccountRelation__c
                WHERE FinServ__Account__c = :accountId AND FinServ__RelatedAccount__c = :idAzienda AND RecordTypeId = :idAccountAgency
                WITH USER_MODE
            ]
            .isEmpty();
        output.put('isAbbinato', !isAbbinato);
    }
    //# Raffaele Granato: Raffaele Granato | UNF-761 - Coni di visibilità - Scheda soggetto

    private void getStatoComuneCF(Map<String, Object> inputs, Map<String, Object> output) {
        String codiceCF = (String) inputs.get('CF');
        String newCF = '';
        for (Integer i = 0; i < codiceCF.length(); i++) {
            String currentChar = codiceCF.substring(i, i + 1);
            System.debug('currentChar: ' + currentChar);
            // Replace characters according to the OMOCODIA map
            if (i == 6 || i == 7 || i == 9 || i == 10 || i == 12 || i == 13 || i == 14) {
                if (OMOCODIA.containsKey(currentChar)) {
                    newCF += OMOCODIA.get(currentChar);
                } else {
                    newCF += currentChar;
                }
            } else {
                newCF += currentChar;
            }
        }
        String stato = (newCF.substring(11, 12) == 'Z') ? (newCF.substring(11, 15)) : 'Z000';
        String comune = (stato == 'Z000') ? (newCF.substring(11, 15)) : null;
        String provincia = (comune != null) ? [SELECT Sigla_automobilistica__c FROM Unita_Territoriali__c WHERE Codice_catasto__c = :comune LIMIT 1]?.Sigla_automobilistica__c : null;
        output.put('stato', stato);
        output.put('comune', comune);
        output.put('provincia', provincia);
    }

    private void stringManipulation(Map<String, Object> inputs, Map<String, Object> output) {
        String inputString = (String) inputs.get('inputString');
        String action = (String) inputs.get('action');
        String outputString;
        if (String.isBlank(inputString)) {
            output.put('errorMessage', 'Input string cannot be empty');
            return;
        }
        switch on action {
            when 'toUpperCase' {
                outputString = inputString.toUpperCase();
            }
            when 'toLowerCase' {
                outputString = inputString.toLowerCase();
            }
            when 'trim' {
                outputString = inputString.trim();
            }
            when else {
                output.put('errorMessage', 'Invalid action specified');
                return;
            }
        }

        output.put('outputString', outputString);
    }

    //Guy De Roquefeuil: aggiunto checkClienteAltraAgenzia............................................
    private void checkClienteAltraAgenzia(Map<String, Object> inputs, Map<String, Object> output) {
        String accountId = (String) inputs.get('accountId');
        //String userId = (String) inputs.get('userId'); //nel caso in cui lo userId arrivasse dalla IP (cosa CONSIGLIATA)
        String userId = UserInfo.getUserId();
        String idAzienda = [SELECT Id, IdAzienda__c FROM User WHERE Id = :userId LIMIT 1].get(0).IdAzienda__c;
        AggregateResult otherAgencyProductCountQuery = [
                SELECT COUNT(id)
                FROM InsurancePolicy
                WHERE NameInsuredId = :accountId AND Agency__c != NULL AND Agency__c != :idAzienda AND (ActiveDate__c > TODAY OR ActiveDate__c = NULL)
            ]
            .get(0);
        Integer otherAgencyProductCount = (Integer) otherAgencyProductCountQuery.get('expr0');
        output.put('isClienteAltraAgenzia', otherAgencyProductCount > 0);
    }
    //..................................................................................................
}
